<svg width="200" height="200" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <style>
    .fox-main-outline {
      stroke: black;
      stroke-width: 2.5; /* 稍粗的主轮廓 */
      fill: none;
      stroke-linecap: round;
      stroke-linejoin: round;
    }
    .fox-secondary-outline {
      stroke: dimgray; /* 深灰色 */
      stroke-width: 1.5; /* 稍细的辅助轮廓 */
      fill: none;
      stroke-linecap: round;
      stroke-linejoin: round;
    }
    .fox-detail-light {
      stroke: silver; /* 更浅的灰色，用于非常细微的暗示 */
      stroke-width: 1;
      fill: none;
      stroke-linecap: round;
      stroke-linejoin: round;
    }
  </style>

  <!-- 主要脸部和耳朵轮廓 (黑色) -->
  <path class="fox-main-outline" d="
    M 50,10
    C 30,15 20,35 22,55  /* 左耳顶部到脸颊 */
    C 23,70 35,88 50,92  /* 脸颊到下巴尖 */
    C 65,88 77,70 78,55  /* 下巴尖到右耳 */
    C 80,35 70,15 50,10   /* 右耳回到头顶 */
    Z
  "/>

  <!-- 内耳轮廓 (深灰色) -->
  <path class="fox-secondary-outline" d="
    M 40,22
    C 35,35 38,50 42,53
  "/>
  <path class="fox-secondary-outline" d="
    M 60,22
    C 65,35 62,50 58,53
  "/>

  <!-- 鼻子 (黑色，可以是一个小形状或几条线) -->
  <path class="fox-main-outline" d="M 48,78 Q 50,82 52,78 Z" />
  <!-- 或者用简单线条:
  <line class="fox-main-outline" x1="48" y1="80" x2="52" y2="80" />
  <line class="fox-main-outline" x1="50" y1="78" x2="50" y2="81" />
  -->


  <!-- 眼睛的暗示 (深灰色或黑色，非常简约) -->
  <!-- 可以是闭眼的感觉，或者非常小的点/短线 -->
  <path class="fox-secondary-outline" d="M 38,60 Q 42,62 46,60" /> <!-- 左眼弧线 -->
  <path class="fox-secondary-outline" d="M 62,60 Q 58,62 54,60" /> <!-- 右眼弧线 -->


  <!-- 可选：脸颊或额头的一些风格化线条 (浅灰色) -->
  <path class="fox-detail-light" d="M 50,15 Q 45,25 48,35" />
  <path class="fox-detail-light" d="M 50,15 Q 55,25 52,35" />

  <!-- 可选：下巴/颈部的毛发暗示 (深灰色) -->
  <path class="fox-secondary-outline" d="M 40,85 Q 50,80 60,85" />
  <path class="fox-secondary-outline" d="M 45,89 Q 50,85 55,89" />

</svg>