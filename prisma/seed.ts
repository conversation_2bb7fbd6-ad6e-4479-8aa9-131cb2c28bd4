import { PrismaClient, ModelCategory, SubscriptionType, SubscriptionStatus, ModelType } from '../lib/generated/prisma'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

const modelSeedData = [
  {
    modelName: "Flux/Schnell",
    modelId: "fal-ai/flux/schnell",
    category: ModelCategory.TEXT_TO_IMAGE,
    provider: "Fal AI",
    description: "Powerful open-source text-to-image model capable of generating highly detailed images.",
    isAvailable: true,
    type: ModelType.STANDARD,
    creditCost: 1,
    parameters: {
      default_width: 1024,
      default_height: 1024,
      supported_resolutions: ["512x512", "768x768", "1024x1024"],
      negative_prompt_support: true
    }
  },
  {
    modelName: "Flux/dev",
    modelId: "fal-ai/flux/dev",
    category: ModelCategory.TEXT_TO_IMAGE,
    provider: "Fal AI",
    description: "Powerful open-source text-to-image model capable of generating highly detailed images.",
    isAvailable: false,
    type: ModelType.PREMIUM,
    creditCost: 10,
    parameters: {
      default_width: 1024,
      default_height: 1024,
      supported_resolutions: ["512x512", "768x768", "1024x1024"],
      negative_prompt_support: true
    }
  }
];

async function main() {
  console.log(`Start seeding ...`)
  for (const modelData of modelSeedData) {
    const model = await prisma.model.upsert({
      where: { modelId: modelData.modelId }, // Use modelId as the unique identifier for upsert
      update: modelData,
      create: modelData,
    })
    console.log(`Created or updated model with id: ${model.id} (modelId: ${model.modelId})`)
  }
  console.log(`Model seeding finished.`)

  // Seed Users and Subscriptions
  console.log(`Start seeding users and subscriptions...`)

  const usersToSeed = [
    {
      email: "<EMAIL>",
      password: "123456", // Plain text, will be hashed
      displayName: "Alice Wonderland",
      subscription: {
        type: SubscriptionType.PREMIUM,
        status: SubscriptionStatus.ACTIVE,
        creditsGrantedPerMonth: 1000,
        creditsRemaining: 500,
        currentPeriodStart: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // Start of current month
        currentPeriodEnd: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1), // Start of next month
      }
    },
    {
      email: "<EMAIL>",
      password: "123456", // Plain text, will be hashed
      displayName: "Bob The Builder",
      subscription: {
        type: SubscriptionType.FREE,
        status: SubscriptionStatus.ACTIVE,
        creditsGrantedPerMonth: 50,
        creditsRemaining: 20,
      }
    }
  ];

  for (const userData of usersToSeed) {
    const hashedPassword = bcrypt.hashSync(userData.password, 10);

    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {
        name: userData.displayName,
        // Potentially update other user fields if needed
      },
      create: {
        email: userData.email,
        passwordHash: hashedPassword,
        name: userData.displayName,
        isActive: true,
        subscription: {
          create: {
            type: userData.subscription.type,
            status: userData.subscription.status,
            creditsGrantedPerMonth: userData.subscription.creditsGrantedPerMonth,
            creditsRemaining: userData.subscription.creditsRemaining,
            currentPeriodStart: userData.subscription.currentPeriodStart,
            currentPeriodEnd: userData.subscription.currentPeriodEnd,
          }
        }
      },
      include: {
        subscription: true // Include subscription to log its details
      }
    });

    console.log(`Created or updated user with email: ${user.email}. Subscription ID: ${user.subscription?.id}, Type: ${user.subscription?.type}`);
  }

  console.log(`User and subscription seeding finished.`)
  console.log(`All seeding finished.`)
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })