import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import { redirect } from 'next/navigation';
import EditHistoryGrid from '@/components/edit-history-grid';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Sparkles, CheckCircle, Clock } from 'lucide-react';

export default async function EditHistoryPage() {
  // 检查用户认证
  const session = await auth();

  if (!session?.user) {
    redirect('/sign-in');
  }

  const userId = session.user.id;

  try {
    // 获取初始编辑图片数据和统计信息
    const [editImagesData, stats] = await Promise.all([
      // 获取第一页编辑图片数据
      prisma.editorMultiImage.findMany({
        where: {
          userId: userId
        },
        include: {
          editor: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 20
      }),

      // 获取统计信息
      Promise.all([
        prisma.editorMultiImage.count({
          where: { userId: userId }
        }),
        prisma.editorMultiImage.count({
          where: {
            userId: userId,
            editor: {
              status: 'COMPLETED'
            }
          }
        }),
        prisma.editorMulti.aggregate({
          where: { userId: userId },
          _sum: { creditsUsed: true }
        }),
        prisma.editorMultiImage.count({
          where: {
            userId: userId,
            editor: {
              status: 'PENDING'
            }
          }
        })
      ])
    ]);

    const [totalEditImages, completedEdits, creditsSum, pendingEdits] = stats;
    const totalCreditsUsed = creditsSum._sum.creditsUsed || 0;

    // 计算分页信息
    const limit = 20;
    const totalPages = Math.ceil(totalEditImages / limit);

    const pagination = {
      currentPage: 1,
      totalPages,
      totalCount: totalEditImages,
      hasNextPage: totalPages > 1,
      hasPrevPage: false,
      limit
    };

    return (
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Image Edit History</h1>
          <p className="text-muted-foreground">
            View and manage all your AI-edited images
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Edits</CardTitle>
              <Edit className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalEditImages}</div>
              <p className="text-xs text-muted-foreground">
                Edited images
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{completedEdits}</div>
              <p className="text-xs text-muted-foreground">
                Successfully completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pendingEdits}</div>
              <p className="text-xs text-muted-foreground">
                In progress
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Credits Used</CardTitle>
              <Sparkles className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalCreditsUsed}</div>
              <p className="text-xs text-muted-foreground">
                Total credits spent
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Edit Images Grid */}
        <EditHistoryGrid
          initialEditImages={editImagesData.map(img => ({
            ...img,
            tmpUrl: img.tmpUrl || undefined,
            createdAt: img.createdAt.toISOString(),
            editor: {
              ...img.editor,
              userPrompt: img.editor.userPrompt || undefined,
              createdAt: img.editor.createdAt.toISOString(),
              completedAt: img.editor.completedAt?.toISOString()
            }
          }))}
          initialPagination={pagination}
        />
      </div>
    );
  } catch (error) {
    console.error('Error fetching edit history:', error);

    return (
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="text-center py-16">
          <Edit size={64} className="text-gray-400 mb-4 mx-auto" />
          <h2 className="text-xl font-semibold text-gray-600 mb-2">
            Failed to load edit history
          </h2>
          <p className="text-gray-500">
            Please try refreshing the page or contact support if the problem persists.
          </p>
        </div>
      </div>
    );
  }
}