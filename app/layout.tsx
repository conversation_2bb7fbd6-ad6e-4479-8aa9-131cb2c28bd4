import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Header from "../components/header";
import Footer from "../components/footer";
import { Toaster } from "../components/ui/sonner";
import { SessionProvider } from "next-auth/react";
import CookieConsent from "../components/cookie-consent";
import { auth } from "@/auth";
import { GoogleAnalytics } from "@next/third-parties/google"
import StructuredData, { OrganizationStructuredData, SoftwareApplicationStructuredData } from "../components/structured-data";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ImageFox - Free AI Image Generator & Photo Editor Online",
  description: "Create stunning images with ImageFox's free AI image generator and editor. Transform text to images, edit photos with AI, and enhance visuals instantly. Start creating amazing AI art today.",
  keywords: "AI image generator, free image generator, text to image, AI photo editor, artificial intelligence art, AI art generator, image creation tool, photo editing online, AI visual content",
  authors: [{ name: "ImageFox" }],
  creator: "ImageFox",
  publisher: "ImageFox",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://imagefox.art'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "ImageFox - Free AI Image Generator & Photo Editor Online",
    description: "Create stunning images with ImageFox's free AI image generator and editor. Transform text to images, edit photos with AI, and enhance visuals instantly.",
    url: 'https://imagefox.art',
    siteName: 'ImageFox',
    images: [
      {
        url: '/logo.png',
        width: 1200,
        height: 630,
        alt: 'ImageFox - AI Image Generator',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "ImageFox - Free AI Image Generator & Photo Editor Online",
    description: "Create stunning images with ImageFox's free AI image generator and editor. Transform text to images, edit photos with AI.",
    images: ['/logo.png'],
    creator: '@imagefox',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

export default function RootLayout({
  children,
  auth,
}: Readonly<{
  children: React.ReactNode;
  auth: React.ReactNode;
}>) {
  
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SessionProvider> {/* 添加 refetchInterval={5} (每5秒刷新一次) */}
        <div className="w-full bg-white text-black font-['Inter']">
          <Header />
          {children}
          <Footer />
        </div>
        
        {auth}
        </SessionProvider>
        <CookieConsent
        companyName="ImageFox"
        privacyPolicyUrl="/privacy-policy"
      />
        <GoogleAnalytics gaId="G-ZBXXHST9LV" />
        <StructuredData type="webapp" />
        <OrganizationStructuredData />
        <SoftwareApplicationStructuredData />
        <Toaster position="top-center" />
      </body>
    </html>
  );
}
