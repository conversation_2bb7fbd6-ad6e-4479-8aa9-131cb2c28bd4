import { LoginForm } from "@/components/LoginForm"
import { auth } from "@/auth"
import { redirect } from "next/navigation"

const SignInPage = async () => {
  const session = await auth()
  if(session){
    redirect('/profile')
  }
  
  
  return (
    <div className="flex flex-col items-center justify-center w-full h-screen gap-y-4">
      <div className="text-2xl font-bold text-center">Sign In</div>
      <LoginForm />
    </div>
  )
}

export default SignInPage