import { redirect } from "next/navigation";
import { verifyHash } from "@/lib/email/verify";
import { prisma } from "@/lib/prisma";

export default async function verify({searchParams}:{searchParams: Promise<{[key:string]:string | string[] | undefined}>}){
    const params = await searchParams;
    const token = params['k'] as string;

    if (!token) {
        return redirect('/');
    }
    const email = await verifyHash(token);
    if(email){
        //update user emailVerified
        await prisma.user.update({
            where: {
                email: email,
            },
            data: {
                emailVerified: new Date(),
            },
        })
    }
    console.log(email)
    
    return (
        <>
        <div className="flex w-full h-screen gap-y-4">
            <div className="flex flex-col items-center justify-center w-full h-32">
            {email && <div className="text-green-500 text-2xl">Email Verify Success</div>}
            {!email && <div className="text-red-500 text-2xl">Email Verify Failed</div>}
            </div>
        </div>
        </>
    )
}
