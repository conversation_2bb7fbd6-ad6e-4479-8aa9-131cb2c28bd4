import { NextResponse } from "next/server";
import { uploadToR2 } from "@/lib/storage";
import { v4 as uuidv4 } from 'uuid';

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = ['jpeg', 'png', 'jpg'];

export async function POST(request: Request) {
    //R2 configuration
    const R2DomainName = process.env.R2_DOMAIN_NAME;

    const formData = await request.formData();
    const files = formData.getAll('file').filter((f): f is File => f instanceof File);
    if (!files || files.length === 0) {
        return NextResponse.json({ status: 'failed', error: 'No file uploaded' });
    }

    let urlList: string[] = [];

    for (const file of files) {
        const extension = file.name.split('.').pop()?.toLowerCase();
        if(!extension){
            return NextResponse.json({ status: 'failed', error: 'File type not allowed' });
        }
        
        //check file size
        if(file.size > MAX_FILE_SIZE){
            return NextResponse.json({ status: 'failed', error: 'File size too large' });
        }
        //check file type
        if(!ALLOWED_TYPES.includes(extension)){
            return NextResponse.json({ status: 'failed', error: 'File type not allowed' });
        }
        const fileName = `${uuidv4()}.${extension}`;
        //upload file to r2
        const buffer = Buffer.from(await file.arrayBuffer());
        const key = await uploadToR2(fileName, buffer);
        if (!key) {
            return NextResponse.json({status: 'failed', error: 'Failed to upload file' });
        }

        urlList.push(`${R2DomainName}/${key}`);
    }

    return NextResponse.json({ status: 'ok', urls: urlList });
}
