'use server'
import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { auth } from "@/auth";
import { enhanceUserPrompt } from "@/lib/ai/image";

export async function POST(req: NextRequest) {
  try {
    // Clone the request to read the body without consuming it
    const body = await req.json();

    // Get the current user session
    const session = await auth()
    if (!session?.user) {
      return new Response(JSON.stringify({status: 'failed', error: "Unauthorized" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // Extract user ID from session
    const userId = session.user.id;
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (!user) {
      return new Response(JSON.stringify({status: 'failed', error: "User not found" }), {
        headers: { "Content-Type": "application/json" }
      });
    }

    // Extract parameters from the request body
    const { prompt, parameters, model_id } = body;
    
  //   // Find the model to get credit cost
    const model = await prisma.model.findUnique({
      where: { id: model_id, isAvailable: true }
    });
    
    if (!model) {
      return new Response(JSON.stringify({status: 'failed', error: "Model not found" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
  const userSubscription = await prisma.subscription.findUnique({
    where: { userId: user.id }
  });
  if (!userSubscription) {
    return new Response(JSON.stringify({status: 'failed', error: "User subscription not found" }), {
      headers: { "Content-Type": "application/json" }
    });
  }
  //calculate the credit cost
  const creditCost = model.creditCost * parameters.num;
  if (userSubscription.creditsRemaining < creditCost) {
    return new Response(JSON.stringify({status: 'failed', error: "Not enough credits" }), {
      headers: { "Content-Type": "application/json" }
    });
  }
  //Enhance the user prompt
  const enhancePrompt = await enhanceUserPrompt(prompt);
  if (enhancePrompt.error) {
    return new Response(JSON.stringify({status: 'failed', error: enhancePrompt.error }), {
      headers: { "Content-Type": "application/json" }
    });
  }

  //update the user subscription
  const updatedSubscription = await prisma.subscription.update({
    where: { userId: user.id },
    data: { 
      creditsRemaining: {
        decrement: creditCost
      }
    }
  });
  //   // Create image generation record
    const imageGeneration = await prisma.imageGeneration.create({
      data: {
        userId: user.id,
        userPrompt: prompt,
        prompt: enhancePrompt.prompt as string,
        modelId: model.id,  
        parameters,
        status: "PENDING",
        creditsUsed: creditCost
      }
    });
    const webhook = `${process.env.NEXTAUTH_URL}/api/callback/fal`;

  //   // Add generation ID to the request headers to track it

  const input = {
        prompt: enhancePrompt.prompt as string,
        num_images: parameters.num,
        image_size:{
          width: parameters.width,
          height: parameters.height
        },
        seed: parameters.seed,
        num_inference_steps: parameters.steps,
        sync_mode: false,
        enable_safety_checker: true,
        //guidance_scale: parameters.guidance
      }

   const responseApi = await fetch(`https://queue.fal.run/${model.modelId}?fal_webhook=${webhook}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Key ${process.env.FAL_KEY}`
      },
      body: JSON.stringify(input)
    });
  const responseData = await responseApi.json();

  //   // Update the image generation record with the task ID
    if (responseData.request_id) {
      await prisma.imageGeneration.update({
        where: { id: imageGeneration.id },
        data: { taskId: responseData.request_id, status: "PENDING", metadata: responseData }
      });
    } else {
      await prisma.imageGeneration.update({
        where: { id: imageGeneration.id },
        data: { status: "FAILED", metadata: responseData }
      });
    }
    
  //   return response and user remaining credits 
  return new Response(JSON.stringify({ status: 'ok', taskId: imageGeneration.id, credits: updatedSubscription.creditsRemaining}));
  
  } catch (error) {
    console.error("Error in FAL proxy:", error);
    return new Response(JSON.stringify({status: 'failed', error: "Internal Server Error" }), {
      headers: { "Content-Type": "application/json" }
    });
  }
}

