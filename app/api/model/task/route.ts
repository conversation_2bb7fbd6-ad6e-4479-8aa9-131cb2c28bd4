import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

const R2_DOMAIN_NAME = process.env.R2_DOMAIN_NAME;

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { taskId } = body;
    
    if (!taskId) {
      return new NextResponse(JSON.stringify({ error: "Missing parameters" }), {
        status: 400,
        headers: { "Content-Type": "application/json" }
      });
    }
    //find imagegeneration by taskId
    const imageGeneration = await prisma.imageGeneration.findUnique({
      select: {
        id: true,
        metadata: true,
        status: true,
        creditsUsed: true
      },
      where: { id: taskId }
    });
    if (!imageGeneration) {
      return new NextResponse(JSON.stringify({ error: "Task not found" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
    if(imageGeneration.status === "COMPLETED"){
      const images = await prisma.image.findMany({
        where: {
          generationId: imageGeneration.id as string
        }
      });
      if(images.length === 0){
        return new NextResponse(JSON.stringify({status: "completed", images: []}));
      }
      const imagePaths = images.map((image) => `${R2_DOMAIN_NAME}/${image.storagePath}`);
      return new NextResponse(JSON.stringify({status: "ok", images: imagePaths}));
    }
    return new NextResponse(JSON.stringify({status: 'pending'}));
  //   const statusResponse = await fetch(`${(imageGeneration.metadata as { status_url: string })?.status_url}`, {
  //       method: 'GET',
  //       headers: {
  //           'Authorization': `Key ${process.env.FAL_KEY}`
  //       }
  //   });
  //   if (!statusResponse.ok) throw new Error('Failed to fetch task');
  //   const statusData = await statusResponse.json();
  //   if(statusData.status !== "COMPLETED"){
  //      return new NextResponse(JSON.stringify({status: statusData.status}));
  //   }
  //   const response = await fetch(`${(statusData as { response_url: string }).response_url}`, {
  //     method: 'GET',
  //     headers: {
  //         'Authorization': `Key ${process.env.FAL_KEY}`
  //     }
  // });
  // if (!response.ok) throw new Error(`Failed to fetch response task ${ (statusData as { response_url: string }).response_url}`);
  // const responseData = await response.json();
  // //images
  // if(responseData.status !== "COMPLETED" && responseData?.images?.length === 0){
  //  return new NextResponse(JSON.stringify({status: responseData.status}));
  // }

  // return new NextResponse(JSON.stringify({status: 'ok', images: responseData.images}));
  } catch (error) {
    return new NextResponse(JSON.stringify({ error: "Internal Server Error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
}
