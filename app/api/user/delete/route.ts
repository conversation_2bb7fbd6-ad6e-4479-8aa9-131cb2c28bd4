import { auth } from "@/auth"
import { NextResponse, type NextRequest } from "next/server"
import { prisma } from "@/lib/prisma"

export async function POST(req: NextRequest) {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ status: 'failed', error: 'Unauthorized' })
    }
    try{
       
        const images = await prisma.image.deleteMany({
        where: { userId: session.user.id }
        });
        if (!images) {
        return NextResponse.json({ status: 'failed', error: 'Images not found' })
        }
        const imageGenerations = await prisma.imageGeneration.deleteMany({
        where: { userId: session.user.id }
        });
        if (!imageGenerations) {
        return NextResponse.json({ status: 'failed', error: 'Image generations not found' })
        }   
        const subscription = await prisma.subscription.delete({
            where: { userId: session.user.id }
          });
         
          if (!subscription) {
            return NextResponse.json({ status: 'failed', error: 'Subscription not found' })
          }
        const user = await prisma.user.delete({
        where: { id: session.user.id }
        });
        if (!user) {
            return NextResponse.json({ status: 'failed', error: 'User not found' })
        }
        return NextResponse.json({ status: 'ok', message: 'User deleted' });
    }catch(error){
      console.error("Error deleting user:", error);
      return NextResponse.json({ status: 'failed', error: 'Internal Server Error' })
    }
}
