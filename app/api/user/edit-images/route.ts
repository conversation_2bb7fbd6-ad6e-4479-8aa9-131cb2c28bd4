import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import { type EditorMultiImage } from '@/lib/generated/prisma';

// GET 获取用户所有编辑图片记录
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // 获取用户ID
    const userId = session.user.id;

    // 获取用户的所有编辑图片记录，包含编辑任务信息
    const [editImages, totalCount] = await Promise.all([
      prisma.editorMultiImage.findMany({
        where: {
          userId: userId
        },
        include: {
          editor: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.editorMultiImage.count({
        where: {
          userId: userId
        }
      })
    ]);
    let images: EditorMultiImage[] = []
    if(editImages.length > 0){
      images = editImages.map((editImage) => {
        return {
          id: editImage.id,
          imageUrl:  editImage.imageUrl,
          tmpUrl: editImage.tmpUrl,
          createdAt: editImage.createdAt,
          editorId: editImage.editor.id,
          updatedAt: editImage.updatedAt,
          userId: editImage.userId
        }
      })
    }

    // 计算分页信息
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      editImages: images,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit
      }
    });
  } catch (error) {
    console.error('获取用户编辑图片记录失败:', error);
    return NextResponse.json({ error: '获取编辑图片记录失败' }, { status: 500 });
  }
}
