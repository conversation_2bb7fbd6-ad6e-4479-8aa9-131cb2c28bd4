import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';

// 获取特定生成任务的所有图片
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ generationId: string }> }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const userId = session.user.id;
    const { generationId } = await params;

    // 检查生成任务是否存在且属于该用户
    const generation = await prisma.imageGeneration.findUnique({
      where: {
        id: generationId,
        userId: userId
      },
      include: {
        images: true
      }
    });

    if (!generation) {
      return NextResponse.json({ error: '生成任务不存在或无权访问' }, { status: 404 });
    }

    // 获取该生成任务的所有图片
    const images = await prisma.image.findMany({
      where: {
        generationId: generationId
      }
    });

    return NextResponse.json({ images });
  } catch (error) {
    console.error('获取生成任务图片失败:', error);
    return NextResponse.json({ error: '获取图片失败' }, { status: 500 });
  }
}
