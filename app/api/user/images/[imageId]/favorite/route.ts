import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ imageId: string }> }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }

    const userId = session.user.id;
    const { imageId } = await params;

    // 检查图片是否存在且属于该用户
    const image = await prisma.image.findUnique({
      where: {
        id: imageId,
        userId: userId
      }
    });

    if (!image) {
      return NextResponse.json({ error: '图片不存在或无权操作' }, { status: 404 });
    }

    // 切换收藏状态
    const updatedImage = await prisma.image.update({
      where: {
        id: imageId
      },
      data: {
        isFavorite: !image.isFavorite
      }
    });

    return NextResponse.json({ 
      success: true, 
      isFavorite: updatedImage.isFavorite 
    });
  } catch (error) {
    console.error('切换收藏状态失败:', error);
    return NextResponse.json({ error: '操作失败' }, { status: 500 });
  }
}
