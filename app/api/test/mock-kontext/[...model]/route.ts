export async function POST(){
    return new Response(JSON.stringify(
        {
            logs: null, 
            status: "IN_QUEUE", 
            metrics: {}, 
            cancel_url: "https://queue.fal.run/fal-ai/flux-pro/requests/71a990ea-5fba-4663-901d-a6f750378101/cancel", 
            request_id: "71a990ea-5fba-4663-901d-a6f750378101", 
            status_url: "https://queue.fal.run/fal-ai/flux-pro/requests/71a990ea-5fba-4663-901d-a6f750378101/status", 
            response_url: "https://queue.fal.run/fal-ai/flux-pro/requests/71a990ea-5fba-4663-901d-a6f750378101", 
            queue_position: 4
        }
    ))
}

export async function GET(){
    return new Response(JSON.stringify(
        {
            logs: null, 
            status: "IN_QUEUE", 
            metrics: {}, 
            cancel_url: "https://queue.fal.run/fal-ai/flux-pro/requests/71a990ea-5fba-4663-901d-a6f750378101/cancel", 
            request_id: "71a990ea-5fba-4663-901d-a6f750378101", 
            status_url: "https://queue.fal.run/fal-ai/flux-pro/requests/71a990ea-5fba-4663-901d-a6f750378101/status", 
            response_url: "https://queue.fal.run/fal-ai/flux-pro/requests/71a990ea-5fba-4663-901d-a6f750378101", 
            queue_position: 4
        }
    ))
}