import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { enhanceUserPrompt } from '@/lib/ai/image';


export async function POST(req: NextRequest) {
  //登陆验证
  const session = await auth()
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }
  const { prompt } = await req.json();
  const response = await enhanceUserPrompt(prompt);
  if (response.error) {
    return NextResponse.json({ error: response.error }, { status: 400 })
  }
  return NextResponse.json({prompt:response.prompt });
}


