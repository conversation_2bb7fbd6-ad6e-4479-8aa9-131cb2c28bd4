import { NextRequest, NextResponse } from "next/server";
import Replicate from "replicate";

// 验证Turnstile token
async function verifyTurnstileToken(token: string): Promise<boolean> {
  const verifyEndpoint = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';
  const secret = process.env.TURNSTILE_SECRET_KEY;

  if (!secret) {
    console.error('TURNSTILE_SECRET_KEY not configured');
    return false;
  }

  try {
    const response = await fetch(verifyEndpoint, {
      method: 'POST',
      body: `secret=${encodeURIComponent(secret)}&response=${encodeURIComponent(token)}`,
      headers: {
        'content-type': 'application/x-www-form-urlencoded'
      }
    });

    const data = await response.json();
    return data.success === true;
  } catch (error) {
    console.error('Error verifying Turnstile token:', error);
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { imageUrl, turnstileToken } = body;

    if (!imageUrl || !turnstileToken) {
      return NextResponse.json({ error: '缺少必要参数' }, { status: 400 });
    }

    // 验证Turnstile token
    const isValidToken = await verifyTurnstileToken(turnstileToken);
    if (!isValidToken) {
      return NextResponse.json({ error: 'Turnstile验证失败' }, { status: 400 });
    }

    // 调用Replicate API - 同步等待结果
    const replicate = new Replicate({
      auth: process.env.REPLICATE_API_TOKEN,
    });

    console.log('Starting background removal for image:', imageUrl);

    const output = await replicate.run(
      "rembg/rembg:fb8af171cfa1616ddcf1242c093f9c46bcada5ad4cf6f2fbe8b81b330ec5c003",
      {
        input: {
          image: imageUrl
        }
      }
    );

    console.log('Background removal completed:', output);

    if (!output) {
      return NextResponse.json({ error: '背景去除失败' }, { status: 500 });
    }

    // Replicate返回的可能是一个URL字符串、ReadableStream或者其他格式
    let processedImageUrl: string;

    if (typeof output === 'string') {
      // 如果是字符串URL，直接使用
      processedImageUrl = output;
    } else if (output instanceof ReadableStream) {
      // 如果是ReadableStream，需要读取并转换为blob URL
      const reader = output.getReader();
      const chunks: Uint8Array[] = [];

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }

      // 合并所有chunks
      const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
      const result = new Uint8Array(totalLength);
      let offset = 0;
      for (const chunk of chunks) {
        result.set(chunk, offset);
        offset += chunk.length;
      }

      // 创建blob并返回base64
      const blob = new Blob([result], { type: 'image/png' });
      const arrayBuffer = await blob.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString('base64');
      processedImageUrl = `data:image/png;base64,${base64}`;
    } else if (output && typeof output === 'object' && 'url' in output) {
      // 如果是包含url属性的对象
      processedImageUrl = (output as any).url;
    } else {
      // 如果是其他格式，尝试转换为字符串
      processedImageUrl = String(output);
    }

    console.log('Processed image URL type:', typeof processedImageUrl);

    return NextResponse.json({
      success: true,
      originalImageUrl: imageUrl,
      processedImageUrl: processedImageUrl
    });

  } catch (error) {
    console.error('Background removal error:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : '服务器内部错误'
    }, { status: 500 });
  }
}
