'use server'
import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/lib/generated/prisma";
import { uploadImageFromUrlToR2 } from "@/lib/storage";

const prisma = new PrismaClient();

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // 记录调试信息
    const debug = await prisma.debug.create({
      data: {
        data: body,
        createdAt: new Date()
      }
    });

    const {
      request_id,
      status,
      payload,
      payload_error
    } = body;

    if (status !== "OK") {
      console.error("Multi-image generation failed for task_id:", request_id);
      
      // 查找对应的EditorMulti记录
      const editorMulti = await prisma.editorMulti.findFirst({
        where: { taskId: request_id }
      });

      if (editorMulti) {
        // 更新状态为失败
        await prisma.editorMulti.update({
          where: { id: editorMulti.id },
          data: {
            status: "FAILED",
            completedAt: new Date()
          }
        });

        // 返还用户积分
        await prisma.subscription.update({
          where: { userId: editorMulti.userId },
          data: { 
            creditsRemaining: {
              increment: editorMulti.creditsUsed
            }
          }
        });
      }

      return NextResponse.json({ error: "Multi-image generation failed" });
    }

    // 查找对应的EditorMulti记录
    const editorMulti = await prisma.editorMulti.findFirst({
      where: { taskId: request_id }
    });

    if (!editorMulti) {
      console.error("Multi-image generation not found for task_id:", request_id);
      return NextResponse.json({ error: "Multi-image generation not found" }, { status: 404 });
    }

    // 检查是否已经完成
    if (editorMulti.status === 'COMPLETED') {
      console.log('Multi-image generation already completed:', request_id);
      return NextResponse.json({ success: true });
    }

    // 更新EditorMulti记录状态
    await prisma.editorMulti.update({
      where: { id: editorMulti.id },
      data: {
        status: status === "OK" ? "COMPLETED" : "FAILED",
        completedAt: new Date()
      }
    });

    // 如果成功且有图片，创建图片记录
    if (status === "OK" && payload && Array.isArray(payload.images)) {
      await Promise.allSettled(
        payload.images.map(async (img: any, index: number) => {
          const key = `${request_id}-${index}`;
          const imagePath = await uploadImageFromUrlToR2(key, img.url);
          if (imagePath) {
            await prisma.editorMultiImage.create({
              data: {
                editorId: editorMulti.id,
                imageUrl: imagePath,
                userId: editorMulti.userId,
                tmpUrl: img.url
              }
            });
          }
        })
      );
    }

    return NextResponse.json({ success: true });
  } catch (err) {
    console.error("Error processing multi-image FAL webhook:", err);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
