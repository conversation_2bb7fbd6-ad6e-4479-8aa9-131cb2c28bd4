//接收一个url参数，然后下载这个图片
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
    const url = request.nextUrl.searchParams.get('url');
    if (!url) {
        return NextResponse.json({ error: 'Missing url' });
    }
    const response = await fetch(url);
    if (!response.ok) {
        return NextResponse.json({ error: 'Failed to fetch image' });
    }
    const blob = await response.blob();
    const headers = new Headers();
    //判断图片类型，根据response返回的Content-Type来判断
    if (response.headers.get('Content-Type')?.includes('image')) {
        headers.set('Content-Type', response.headers.get('Content-Type') || 'image/jpeg');
    } else {
        headers.set('Content-Type', 'application/octet-stream');
    }
    headers.set('Content-Disposition', 'attachment');
    return new NextResponse(blob, { headers });
}