import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

const R2_DOMAIN_NAME = process.env.R2_DOMAIN_NAME;

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { taskId } = body;
    
    if (!taskId) {
      return new NextResponse(JSON.stringify({ error: "Missing parameters" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
    
    // 查找EditorMulti记录
    const editorMulti = await prisma.editorMulti.findUnique({
      select: {
        id: true,
        status: true
      },
      where: { id: taskId }
    });
    
    if (!editorMulti) {
      return new NextResponse(JSON.stringify({ error: "Task not found" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
    
    if (editorMulti.status === "COMPLETED") {
      // 获取生成的图片
      const images = await prisma.editorMultiImage.findMany({
        where: {
          editorId: editorMulti.id
        }
      });
      
      if (images.length === 0) {
        return new NextResponse(JSON.stringify({status: "completed", images: []}));
      }
      
      const imagePaths = images.map((image) => `${R2_DOMAIN_NAME}/${image.imageUrl}`);
      return new NextResponse(JSON.stringify({status: "ok", images: imagePaths}));
    }
    
    if (editorMulti.status === "FAILED") {
      return new NextResponse(JSON.stringify({status: "failed", error: "Image generation failed"}));
    }
    
    return new NextResponse(JSON.stringify({status: 'pending'}));
  } catch (error) {
    console.error("Error in editor-multi-task API:", error);
    return new NextResponse(JSON.stringify({ error: "Internal Server Error" }), {
      headers: { "Content-Type": "application/json" }
    });
  }
}
