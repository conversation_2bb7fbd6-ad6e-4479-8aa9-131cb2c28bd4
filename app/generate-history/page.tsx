import { auth } from '@/auth';
import { prisma } from '@/lib/prisma';
import { redirect } from 'next/navigation';
import ImageHistoryGrid from '@/components/image-history-grid';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ImageI<PERSON>, <PERSON>rk<PERSON>, Heart } from 'lucide-react';

export default async function GenerateHistoryPage() {
  // 检查用户认证
  const session = await auth();

  if (!session?.user) {
    redirect('/sign-in');
  }

  const userId = session.user.id;

  try {
    // 获取初始图片数据和统计信息
    const [imagesData, stats] = await Promise.all([
      // 获取第一页图片数据
      prisma.image.findMany({
        where: {
          userId: userId
        },
        include: {
          generation: {
            include: {
              model: true
            }
          }
        },
        orderBy: {
          generation: {
            createdAt: 'desc'
          }
        },
        take: 20
      }),

      // 获取统计信息
      Promise.all([
        prisma.image.count({
          where: { userId: userId }
        }),
        prisma.image.count({
          where: { userId: userId, isFavorite: true }
        }),
        prisma.imageGeneration.aggregate({
          where: { userId: userId },
          _sum: { creditsUsed: true }
        })
      ])
    ]);

    const [totalImages, favoriteImages, creditsSum] = stats;
    const totalCreditsUsed = creditsSum._sum.creditsUsed || 0;

    // 计算分页信息
    const limit = 20;
    const totalPages = Math.ceil(totalImages / limit);

    const pagination = {
      currentPage: 1,
      totalPages,
      totalCount: totalImages,
      hasNextPage: totalPages > 1,
      hasPrevPage: false,
      limit
    };

    return (
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Image Generation History</h1>
          <p className="text-muted-foreground">
            View and manage all your AI-generated images
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Images</CardTitle>
              <ImageIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalImages}</div>
              <p className="text-xs text-muted-foreground">
                Generated images
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Favorites</CardTitle>
              <Heart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{favoriteImages}</div>
              <p className="text-xs text-muted-foreground">
                Favorited images
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Credits Used</CardTitle>
              <Sparkles className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalCreditsUsed}</div>
              <p className="text-xs text-muted-foreground">
                Total credits spent
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Images Grid */}
        <ImageHistoryGrid
          initialImages={imagesData.map(img => ({
            ...img,
            thumbnailUrl: img.thumbnailUrl || undefined,
            generation: {
              ...img.generation,
              negativePrompt: img.generation.negativePrompt || undefined,
              createdAt: img.generation.createdAt.toISOString()
            }
          }))}
          initialPagination={pagination}
        />
      </div>
    );
  } catch (error) {
    console.error('Error fetching image history:', error);

    return (
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="text-center py-16">
          <ImageIcon size={64} className="text-gray-400 mb-4 mx-auto" />
          <h2 className="text-xl font-semibold text-gray-600 mb-2">
            Failed to load image history
          </h2>
          <p className="text-gray-500">
            Please try refreshing the page or contact support if the problem persists.
          </p>
        </div>
      </div>
    );
  }
}