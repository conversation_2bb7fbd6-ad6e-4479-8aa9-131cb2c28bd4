import PhotoEditor from "@/components/photo-editor";
import PhotoEditorDetail from "./detail";
import { Metadata } from "next";

export const metadata: Metadata = {
    title: "Free AI Photo Editor - Edit Photos with AI | ImageFox",
    description: "Edit photos with AI for free. Remove backgrounds, enhance images, and apply AI-powered edits to your photos instantly. Professional photo editing made simple.",
    keywords: "free AI photo editor, AI photo editing, background removal, photo enhancement, AI image editing, online photo editor",
    openGraph: {
      title: "Free AI Photo Editor - Edit Photos with AI | ImageFox",
      description: "Edit photos with AI for free. Remove backgrounds, enhance images, and apply AI-powered edits to your photos instantly.",
      url: 'https://imagefox.art/photo-editor',
      images: [
        {
          url: '/logo.png',
          width: 1200,
          height: 630,
          alt: 'ImageFox AI Photo Editor',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: "Free AI Photo Editor - Edit Photos with AI | ImageFox",
      description: "Edit photos with AI for free. Remove backgrounds, enhance images, and apply AI-powered edits to your photos instantly.",
      images: ['/logo.png'],
    },
    alternates: {
      canonical: '/photo-editor',
    },
  };

export default async function PhotoEditorPage() {
    
    return (
        <main className="max-w-7xl mx-auto">
            <div className="flex flex-col gap-2 text-center w-full md:w-2/3 mx-auto md:mt-20 mt-12 md:mb-12 mb-8">
                <h1 className="text-4xl font-bold">Advanced AI Photo Editor: Single & Multi-Image Capabilities</h1>
                <p className="text-gray-600 text-left p-2 md:p-4">Unlock the full potential of AI photo editing. Our editor handles everything from precise single-image adjustments—like background removal and smart enhancements—to complex multi-image operations. Use text prompts with multiple photos to creatively synthesize new visuals, seamlessly blend elements, and achieve professional-quality results effortlessly.</p>
            </div>
            <PhotoEditor/>
            <PhotoEditorDetail/>
        </main>
    )
}