'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';

// Updated interface based on new API response
interface GenerationType {
  id: string;
  status: 'INIT' | 'PENDING' | 'COMPLETED' | 'FAILED';
  completedAt?: string | null;
  createdAt: string;
  metadata: any; // JSON field
  user: {
    email: string | null;
  };
  model: {
    modelName: string;
  };
  images: {
    imageUrl: string;
    thumbnailUrl?: string | null;
  }[];
}

const GeneratorList = () => {
  const [generations, setGenerations] = useState<GenerationType[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchGenerations = async (page: number) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/backend/images?page=${page}&limit=12`);
      if (!response.ok) {
        throw new Error('Failed to fetch generations');
      }
      const data = await response.json();
      setGenerations(data.generations);
      setTotalPages(data.totalPages);
      setCurrentPage(data.currentPage);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGenerations(currentPage);
  }, [currentPage]);

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const renderSkeleton = () => (
    <TableRow>
      <TableCell colSpan={7} className="h-24 text-center">
        <div className="space-y-2 flex flex-col items-center">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </TableCell>
    </TableRow>
  );

  return (
    <div className="container mx-auto py-10">
      <Card>
        <CardHeader>
          <CardTitle>Image Generation Management</CardTitle>
        </CardHeader>
        <CardContent>
          {error && <p className="text-red-500">{error}</p>}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Thumbnail</TableHead>
                <TableHead>Generator ID</TableHead>
                <TableHead>User Email</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Model</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: 12 }).map((_, i) => renderSkeleton())
                : generations.map((gen) => (
                    <TableRow key={gen.id}>
                      <TableCell>
                        {gen.images.length > 0 && (
                          <Image
                            src={gen.images[0].thumbnailUrl || gen.images[0].imageUrl}
                            alt="Generated Image"
                            width={64}
                            height={64}
                            className="rounded-md object-cover"
                          />
                        )}
                      </TableCell>
                      <TableCell className="font-mono text-xs">{gen.id}</TableCell>
                      <TableCell>{gen.user.email || 'N/A'}</TableCell>
                      <TableCell>
                        <Badge variant={gen.status === 'COMPLETED' ? 'default' : 'secondary'}>
                          {gen.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{gen.model.modelName}</TableCell>
                      <TableCell>
                        {format(new Date(gen.createdAt), 'PPpp')}
                      </TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline">View Details</Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[625px]">
                            <DialogHeader>
                              <DialogTitle>Generation Details</DialogTitle>
                              <DialogDescription>
                                Detailed information about the generation for user {gen.user.email}.
                              </DialogDescription>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                              <div className="grid grid-cols-4 items-center gap-4">
                                <Label className="text-right">Metadata</Label>
                                <pre className="col-span-3 bg-gray-100 p-4 rounded-md text-xs overflow-auto dark:bg-gray-800">
                                  {JSON.stringify(gen.metadata, null, 2)}
                                </pre>
                              </div>
                              <div className="grid grid-cols-4 items-center gap-4">
                                <Label className="text-right">Info</Label>
                                <div className="col-span-3 space-y-1 text-sm">
                                  <p><strong>Generator ID:</strong> {gen.id}</p>
                                  <p><strong>User Email:</strong> {gen.user.email || 'N/A'}</p>
                                  <p><strong>Model:</strong> {gen.model.modelName}</p>
                                  <p><strong>Status:</strong> {gen.status}</p>
                                  <p><strong>Created:</strong> {format(new Date(gen.createdAt), 'PPpp')}</p>
                                  {gen.completedAt && <p><strong>Completed:</strong> {format(new Date(gen.completedAt), 'PPpp')}</p>}
                                </div>
                                <div className="col-span-4">
                                  <Label>Images</Label>
                                  <div className="flex flex-wrap gap-2">
                                    {gen.images.map((img, i) => (
                                      <Image
                                        key={i}
                                        src={img.imageUrl}
                                        alt={`Generated Image ${i}`}
                                        width={128}
                                        height={128}
                                        className="rounded-md object-cover"
                                      />
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))}
            </TableBody>
          </Table>
          <div className="flex items-center justify-end space-x-2 py-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrevPage}
              disabled={currentPage <= 1 || loading}
            >
              Previous
            </Button>
            <span className="text-sm">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextPage}
              disabled={currentPage >= totalPages || loading}
            >
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default GeneratorList;

