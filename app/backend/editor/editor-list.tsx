'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';

// Define the interface for the editor data
interface EditorType {
  id: string;
  status: 'INIT' | 'PENDING' | 'COMPLETED' | 'FAILED';
  completedAt?: string | null;
  createdAt: string;
  metadata: any; // JSON field
  user: {
    email: string | null;
  };
  model: {
    modelName: string;
  };
  images: {
    imageUrl: string;
    thumbnailUrl?: string | null;
  }[];
}

const EditorList = () => {
  const [items, setItems] = useState<EditorType[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchItems = async (page: number) => {
    setLoading(true);
    setError(null);
    try {
      // NOTE: Using a placeholder API endpoint. This will need to be updated.
      const response = await fetch(`/api/backend/editors?page=${page}&limit=12`);
      if (!response.ok) {
        throw new Error('Failed to fetch editor data');
      }
      const data = await response.json();
      setItems(data.items);
      setTotalPages(data.totalPages);
      setCurrentPage(data.currentPage);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItems(currentPage);
  }, [currentPage]);

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const renderSkeleton = () => (
    <TableRow>
      <TableCell colSpan={7} className="h-24 text-center">
        <div className="space-y-2 flex flex-col items-center">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </TableCell>
    </TableRow>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Editor List</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Image</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Model</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Completed At</TableHead>
              <TableHead>Details</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading
              ? Array.from({ length: 5 }).map((_, i) => renderSkeleton())
              : items.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      {item.images[0]?.thumbnailUrl && (
                        <Image
                          src={item.images[0].thumbnailUrl}
                          alt="thumbnail"
                          width={50}
                          height={50}
                          className="rounded-md"
                        />
                      )}
                    </TableCell>
                    <TableCell>{item.user.email}</TableCell>
                    <TableCell>{item.model.modelName}</TableCell>
                    <TableCell>
                      <Badge variant={item.status === 'COMPLETED' ? 'default' : 'secondary'}>
                        {item.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{format(new Date(item.createdAt), 'PPpp')}</TableCell>
                    <TableCell>
                      {item.completedAt ? format(new Date(item.completedAt), 'PPpp') : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline">View</Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Details</DialogTitle>
                            <DialogDescription>
                              <pre>{JSON.stringify(item.metadata, null, 2)}</pre>
                            </DialogDescription>
                          </DialogHeader>
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))}
          </TableBody>
        </Table>
        <div className="flex justify-between items-center mt-4">
          <Button onClick={handlePrevPage} disabled={currentPage <= 1}>
            Previous
          </Button>
          <span>
            Page {currentPage} of {totalPages}
          </span>
          <Button onClick={handleNextPage} disabled={currentPage >= totalPages}>
            Next
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default EditorList;
