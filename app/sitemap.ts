import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://imagefox.art';

  // 静态路由 - 按优先级排序
  const staticRoutes = [
    // 主要功能页面 - 最高优先级
    { url: '/', lastModified: new Date(), changeFrequency: 'daily' as const, priority: 1.0 },
    { url: '/free-image-generator', lastModified: new Date(), changeFrequency: 'weekly' as const, priority: 0.95 },
    { url: '/photo-editor', lastModified: new Date(), changeFrequency: 'weekly' as const, priority: 0.9 },
    { url: '/background-remover', lastModified: new Date(), changeFrequency: 'weekly' as const, priority: 0.85 },

    // 用户功能页面
    { url: '/generate-history', lastModified: new Date(), changeFrequency: 'daily' as const, priority: 0.7 },
    { url: '/edit-history', lastModified: new Date(), changeFrequency: 'daily' as const, priority: 0.7 },

    // 信息页面
    { url: '/about-us', lastModified: new Date(), changeFrequency: 'monthly' as const, priority: 0.6 },
    { url: '/contact-us', lastModified: new Date(), changeFrequency: 'monthly' as const, priority: 0.6 },

    // 法律页面
    { url: '/privacy-policy', lastModified: new Date(), changeFrequency: 'monthly' as const, priority: 0.5 },
    { url: '/terms-of-use', lastModified: new Date(), changeFrequency: 'monthly' as const, priority: 0.5 },

    // 认证页面 - 较低优先级
    { url: '/sign-in', lastModified: new Date(), changeFrequency: 'monthly' as const, priority: 0.3 },
    { url: '/sign-up', lastModified: new Date(), changeFrequency: 'monthly' as const, priority: 0.3 },
  ];

  // 动态路由 - 这里需要您根据实际项目添加动态生成的路由
  // 例如，如果您有博客文章或产品页面，可以从数据库或API获取这些路由
  // const dynamicRoutes = await getDynamicRoutes();

  // 合并所有路由
  const allRoutes = [
    ...staticRoutes,
    // ...dynamicRoutes
  ];

  // 转换为sitemap格式
  return allRoutes.map(route => ({
    url: `${baseUrl}${route.url}`,
    lastModified: route.lastModified,
    changeFrequency: route.changeFrequency || 'monthly',
    priority: route.priority || 0.5,
  }));
}

// 示例：从API或数据库获取动态路由
// async function getDynamicRoutes() {
//   try {
//     const response = await fetch('YOUR_API_ENDPOINT/posts');
//     const posts = await response.json();
//     
//     return posts.map(post => ({
//       url: `/posts/${post.slug}`,
//       lastModified: new Date(post.updatedAt || post.createdAt || new Date())
//     }));
//   } catch (error) {
//     console.error('Error fetching dynamic routes:', error);
//     return [];
//   }
// }
