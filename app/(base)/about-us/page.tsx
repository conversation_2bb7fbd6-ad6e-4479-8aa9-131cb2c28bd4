import { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "About Us | SaaSPlatform",
  description: "Learn about SaaSPlatform's mission, vision, and team information.",
  keywords: "SaaSPlatform, About Us, Company Mission, Team Introduction, SaaS Services",
};

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      <h1 className="text-4xl font-bold mb-8 text-center">About Us</h1>
      
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Our Mission</h2>
        <p className="text-lg mb-4">
          PhotoAI is dedicated to providing innovative Software as a Service (SaaS) solutions for businesses and individuals, helping them improve efficiency, reduce costs, and achieve digital transformation.
        </p>
        <p className="text-lg mb-4">
          We believe that technology should be an empowering tool, not a barrier. Through our platform, users can easily create, manage, and optimize their digital assets without needing extensive technical expertise.
        </p>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Our Vision</h2>
        <p className="text-lg mb-4">
          Our vision is to become a leading global SaaS provider, helping organizations worldwide achieve their digital goals through continuous innovation and excellent service.
        </p>
        <p className="text-lg mb-4">
          We are committed to building a more connected, efficient, and sustainable digital future, making the power of technology accessible to everyone.
        </p>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Our Team</h2>
        <p className="text-lg mb-4">
          PhotoAI is comprised of a passionate group of technology experts, designers, and business professionals who possess extensive industry experience and innovative thinking.
        </p>
        <p className="text-lg mb-4">
          Our team members come from diverse backgrounds and cultures, and this diversity enables us to approach problems from multiple perspectives and provide comprehensive solutions for our clients.
        </p>
      </section>

      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Our Values</h2>
        <div className="grid md:grid-cols-2 gap-6">
          <div className="p-6 border rounded-lg shadow-sm">
            <h3 className="text-xl font-medium mb-2">Innovation</h3>
            <p>We continuously explore new technologies and methods to drive industry advancement.</p>
          </div>
          <div className="p-6 border rounded-lg shadow-sm">
            <h3 className="text-xl font-medium mb-2">Customer First</h3>
            <p>We prioritize the needs and success of our customers, providing an excellent service experience.</p>
          </div>
          <div className="p-6 border rounded-lg shadow-sm">
            <h3 className="text-xl font-medium mb-2">Integrity and Transparency</h3>
            <p>We maintain honesty and transparency in all our business dealings, building long-term trust.</p>
          </div>
          <div className="p-6 border rounded-lg shadow-sm">
            <h3 className="text-xl font-medium mb-2">Collaboration and Win-Win</h3>
            <p>We believe that through collaboration and knowledge sharing, we can create greater value.</p>
          </div>
        </div>
      </section>

      <section>
        <h2 className="text-2xl font-semibold mb-4">Contact Us</h2>
        <p className="text-lg mb-4">
          If you have any questions or suggestions, please feel free to contact us. We look forward to hearing from you and providing the support you need.
        </p>
        <div className="flex flex-col md:flex-row gap-4 justify-center">
          <Link href="/contact-us" className="px-6 py-3 bg-primary text-primary-foreground rounded-md text-center hover:bg-primary/90 transition-colors">Contact Us</Link>
        </div>
      </section>
    </div>
  );
}