import Script from 'next/script'

interface StructuredDataProps {
  type?: 'website' | 'webapp' | 'article' | 'product'
  data?: Record<string, any>
}

export default function StructuredData({ type = 'website', data = {} }: StructuredDataProps) {
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": type === 'webapp' ? 'WebApplication' : 'WebSite',
      "name": "ImageFox",
      "description": "Create stunning images with ImageFox's free AI image generator and editor. Transform text to images, edit photos with AI, and enhance visuals instantly.",
      "url": "https://imagefox.art",
      "logo": {
        "@type": "ImageObject",
        "url": "https://imagefox.art/logo.png",
        "width": 512,
        "height": 512
      },
      "sameAs": [
        "https://twitter.com/imagefox",
        "https://facebook.com/imagefox",
        "https://instagram.com/imagefox"
      ],
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://imagefox.art/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }

    if (type === 'webapp') {
      return {
        ...baseData,
        "@type": "WebApplication",
        "applicationCategory": "DesignApplication",
        "operatingSystem": "Web Browser",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock"
        },
        "featureList": [
          "AI Image Generation",
          "Text to Image",
          "Photo Editing",
          "Background Removal",
          "Image Enhancement"
        ]
      }
    }

    return { ...baseData, ...data }
  }

  return (
    <Script
      id="structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(getStructuredData()),
      }}
    />
  )
}

// 专门的组织结构化数据
export function OrganizationStructuredData() {
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "ImageFox",
    "description": "AI-powered image generation and editing platform",
    "url": "https://imagefox.art",
    "logo": {
      "@type": "ImageObject",
      "url": "https://imagefox.art/logo.png",
      "width": 512,
      "height": 512
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": [
      "https://twitter.com/imagefox",
      "https://facebook.com/imagefox",
      "https://instagram.com/imagefox"
    ]
  }

  return (
    <Script
      id="organization-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(organizationData),
      }}
    />
  )
}

// 软件应用结构化数据
export function SoftwareApplicationStructuredData() {
  const softwareData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "ImageFox AI Image Generator",
    "description": "Free AI-powered image generation and photo editing tool",
    "url": "https://imagefox.art",
    "applicationCategory": "DesignApplication",
    "operatingSystem": "Web Browser",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "1250",
      "bestRating": "5",
      "worstRating": "1"
    },
    "featureList": [
      "AI Image Generation from Text",
      "Photo Editing with AI",
      "Background Removal",
      "Image Enhancement",
      "Multiple Art Styles",
      "High Resolution Output"
    ],
    "screenshot": "https://imagefox.art/screenshot.png",
    "author": {
      "@type": "Organization",
      "name": "ImageFox"
    }
  }

  return (
    <Script
      id="software-app-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(softwareData),
      }}
    />
  )
}
