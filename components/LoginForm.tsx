'use client'

import * as React from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Github} from "lucide-react"
import { signIn } from "next-auth/react"
import Turnstile, { useTurnstile } from "react-turnstile"
import Link from "next/link"

export function LoginForm() {
  const [error, setError] = React.useState<string | null>(null);
  const [isCredentialsLoading, setIsCredentialsLoading] = React.useState(false);
  const [isGitHubLoading, setIsGitHubLoading] = React.useState(false);
  const [isRegisterDialogOpen, setIsRegisterDialogOpen] = React.useState(false); // Add state
  const [turnstileToken, setTurnstileToken] = React.useState<string | null>(null);
  const turnstile = useTurnstile(); // Move useTurnstile to component top

  const handleCredentialsSignIn = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setError(null)
    setIsCredentialsLoading(true)
    const formData = new FormData(event.currentTarget)
    const email = formData.get('email') as string
    const password = formData.get('password') as string
    try {
      const result = await signIn('credentials', {
        email,
        password,
        token: turnstileToken,
        redirect: false,
      })
      console.log(result)
      if (result?.error) {
        if (result.error === "CredentialsSignin") {
          setError("Invalid email or password.")
        } else {
          setError(result.error)
        }
      } 
    } catch (err) {
      setError("An unexpected error occurred during login.")
      console.error(err)
    } finally {
      setIsCredentialsLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setIsGitHubLoading(true)
    setError(null)
    try {
      // GitHub login usually involves a full page redirect
      // callbackUrl is set to the current page to return after login
      await signIn('google', { callbackUrl: window.location.href })
      // If signIn triggers a redirect, the code below may not execute immediately
    } catch (err) {
      setError("An unexpected error occurred during Google login.")
      console.error(err)
      setIsGitHubLoading(false) // Only set if signIn throws before redirect
    }
    // If a redirect occurs, the component will reinitialize on page reload, so no need to set setIsGitHubLoading(false) here
  }

  return (
    <>
      <div className=" w-full sm:w-[400px] mx-auto ">
          
        <div className="space-y-4 py-4">
          <Button 
            onClick={handleGoogleSignIn} 
            className="w-full cursor-pointer"
            variant="outline"
            disabled={isGitHubLoading || isCredentialsLoading}
          >
            <div className="flex items-center justify-center">
            <span className="mr-2">
              <svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" xmlnsXlink="http://www.w3.org/1999/xlink" style={{ display: "block" }}>
              <path fill="#EA4335" d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"></path>
              <path fill="#4285F4" d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"></path>
              <path fill="#FBBC05" d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"></path>
              <path fill="#34A853" d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"></path>
              <path fill="none" d="M0 0h48v48H0z"></path>
            </svg>
            </span>
            {isGitHubLoading ? "Redirecting..." : "Sign in with Google"}
            </div>
          </Button>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or sign in with Email
              </span>
            </div>
          </div>
          
          {error && (
            <div className="bg-destructive/15 p-3 rounded-md flex items-center gap-x-2 text-sm text-destructive">
              {/* You can add an error icon */}
              <p>{error}</p>
            </div>
          )}
          <form onSubmit={handleCredentialsSignIn} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email-dialog">Email</Label>
              <Input 
                id="email-dialog" 
                name="email" 
                type="email" 
                placeholder="<EMAIL>"
                required
                disabled={isCredentialsLoading || isGitHubLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password-dialog">Password</Label>
              <Input
                id="password-dialog"
                name="password"
                type="password"
                required
                disabled={isCredentialsLoading || isGitHubLoading}
              />
            </div>
            <Turnstile
              sitekey={'0x4AAAAAABPToBobzrFbjtye'}
              onVerify={(token) => setTurnstileToken(token)}
            />
            <Button type="submit" className="w-full cursor-pointer" disabled={isCredentialsLoading || isGitHubLoading}>
              {isCredentialsLoading ? "Signing in..." : "Sign In"}
            </Button>
          </form>
        </div>
        <div className="flex flex-col space-y-2 text-center text-sm text-muted-foreground sm:flex-row sm:justify-center">
          <div>
            Don't have an account?
            <Link href="/sign-up">
            <Button
              variant="link"
              className="p-0 h-auto underline cursor-pointer"
              onClick={() => {
                setIsRegisterDialogOpen(true); // Open register dialog
              }}
            >
              Sign Up  
            </Button>
            </Link>
          </div>  
        </div>
      </div>
  </>
  )
}
