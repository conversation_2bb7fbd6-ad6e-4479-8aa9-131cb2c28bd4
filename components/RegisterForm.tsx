'use client'

import * as React from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { signUp } from "@/app/(login)/action" // Make sure the path is correct
import Link from "next/link"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import Turnstile from "react-turnstile"

export function RegisterForm() {
  const session = useSession()

  const [turnstileToken, setTurnstileToken] = React.useState<string | null>(null)
  const [isRegistering, setIsRegistering] = React.useState(false)
  const [registrationMessage, setRegistrationMessage] = React.useState<string | null>(null)
  const [isSuccess, setIsSuccess] = React.useState(false)
  const formRef = React.useRef<HTMLFormElement>(null);
  const router = useRouter()
  React.useEffect(() => {
    if (session.status === "authenticated") {
      router.push('/')
    }
  }, [session])

  const handleRegister = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    setIsRegistering(true)
    setRegistrationMessage(null)
    setIsSuccess(false)

    const formData = new FormData(event.currentTarget)
    // Simple frontend password match validation
    const password = formData.get('password') as string
    const confirmPassword = formData.get('confirmPassword') as string

    if (password !== confirmPassword) {
      setRegistrationMessage("Passwords do not match.")
      setIsRegistering(false)
      return
    }
    if (!turnstileToken) {
      setRegistrationMessage("Please complete the turnstile verification.")
      setIsRegistering(false)
      return
    }
    formData.set('token', turnstileToken)

    try {
      const result = await signUp(null, formData) // prevState is null

      if (result.status === 'ok') {
        setIsSuccess(true)
        setRegistrationMessage("Registration successful! You can now log in.")  
        formRef.current?.reset(); // Clear the form after successful registration
      } else if (result.status === 'failed') {
        setRegistrationMessage(result.error?.message || "Registration failed, please try again.")
      } else {
        setRegistrationMessage("An unknown error occurred, please try again.")
      }
    } catch (error) {
      console.error("Registration error:", error)
      setRegistrationMessage("An unexpected error occurred during registration.")
    } finally {
      setIsRegistering(false)
    }
  }


  // Reset state when the dialog is closed, so it's in the initial state next time it's opened
  React.useEffect(() => {
    setRegistrationMessage(null);
    setIsSuccess(false);
    setIsRegistering(false);
    formRef.current?.reset();
  }, []);

  return (
   <div className="w-full sm:w-[400px] mx-auto ">
            
            {registrationMessage && (
              <div className={` text-center p-3 rounded-md text-sm my-4 ${isSuccess ? 'text-green-700' : 'text-destructive'}`}>
                {registrationMessage} <Link href="/sign-in" className="text-gray-500">Sign In</Link>
              </div>
            )}

        {!isSuccess && (
        <>
          <form ref={formRef} onSubmit={handleRegister} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email-register">Email</Label>
              <Input id="email-register" name="email" type="email" placeholder="<EMAIL>" required disabled={isRegistering} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password-register">Password</Label>
              <Input id="password-register" name="password" type="password" required disabled={isRegistering} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword-register">Confirm Password</Label>
              <Input id="confirmPassword-register" name="confirmPassword" type="password" required disabled={isRegistering} />
            </div>
            <div>
              <Turnstile
                sitekey={'0x4AAAAAABPToBobzrFbjtye'}
                onVerify={(token) => setTurnstileToken(token)}
              />  
            </div>
            <Button type="submit" className="w-full cursor-pointer" disabled={isRegistering}>
              {isRegistering ? "Registering..." : "Sign Up"}
            </Button>
          </form>
          </> 
          )}
        
          {!isSuccess && (
            <div className="text-center">
            Already have an account?
            <Link href="/sign-in">
              Sign In
            </Link>
            </div>
              )}
    </div>
  )
}