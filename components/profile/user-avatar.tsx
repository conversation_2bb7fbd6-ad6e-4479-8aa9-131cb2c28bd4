'use client'

import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Upload } from "lucide-react";

interface UserAvatarProps {
  avatarUrl?: string;
  email?: string;
  size?: 'sm' | 'md' | 'lg';
  showUploadOverlay?: boolean;
}

export default function UserAvatar({ 
  avatarUrl, 
  email, 
  size = 'md',
  showUploadOverlay = false 
}: UserAvatarProps) {
  const sizeClasses = {
    sm: 'h-10 w-10',
    md: 'h-16 w-16',
    lg: 'h-24 w-24'
  };

  return (
    <div className="relative group">
      <Avatar className={`${sizeClasses[size]} border-4 border-background shadow-md`}>
        <AvatarImage src={avatarUrl} alt={email || "用户头像"} />
        <AvatarFallback className="text-2xl bg-gradient-to-br from-blue-400 to-indigo-600 text-white">
          {email?.charAt(0) || "U"}
        </AvatarFallback>
      </Avatar>
      {showUploadOverlay && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-full opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer">
          <Upload className="h-6 w-6 text-white" />
        </div>
      )}
    </div>
  );
}
