import Link from 'next/link';
import React from 'react';

// 内部链接组件，用于SEO优化
export function InternalLinks() {
  const links = [
    {
      href: '/free-image-generator',
      text: 'Free AI Image Generator',
      description: 'Create stunning AI images from text descriptions'
    },
    {
      href: '/photo-editor',
      text: 'AI Photo Editor',
      description: 'Edit and enhance photos with AI technology'
    },
    {
      href: '/background-remover',
      text: 'AI Background Remover',
      description: 'Remove image backgrounds instantly with AI'
    }
  ];

  return (
    <section className="py-12 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-bold text-center mb-8">Explore Our AI Tools</h2>
        <div className="grid md:grid-cols-3 gap-6">
          {links.map((link, index) => (
            <Link
              key={index}
              href={link.href}
              className="block p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300"
            >
              <h3 className="text-lg font-semibold mb-2 text-blue-600">{link.text}</h3>
              <p className="text-gray-600">{link.description}</p>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}

// 相关页面链接组件
export function RelatedPages({ currentPage }: { currentPage: string }) {
  const allPages = {
    'home': {
      title: 'ImageFox - AI Image Generator',
      href: '/',
      description: 'Free AI image generator and photo editor'
    },
    'generator': {
      title: 'Free AI Image Generator',
      href: '/free-image-generator',
      description: 'Create images from text with AI'
    },
    'editor': {
      title: 'AI Photo Editor',
      href: '/photo-editor',
      description: 'Edit photos with AI technology'
    },
    'background-remover': {
      title: 'AI Background Remover',
      href: '/background-remover',
      description: 'Remove backgrounds with AI'
    },
    'about': {
      title: 'About ImageFox',
      href: '/about-us',
      description: 'Learn about our AI technology'
    }
  };

  // 获取除当前页面外的其他页面
  const relatedPages = Object.entries(allPages)
    .filter(([key]) => key !== currentPage)
    .slice(0, 3);

  if (relatedPages.length === 0) return null;

  return (
    <section className="py-8 border-t border-gray-200">
      <div className="max-w-4xl mx-auto px-4">
        <h3 className="text-lg font-semibold mb-4">Related Pages</h3>
        <div className="grid md:grid-cols-3 gap-4">
          {relatedPages.map(([key, page]) => (
            <Link
              key={key}
              href={page.href}
              className="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors duration-200"
            >
              <h4 className="font-medium text-blue-600 mb-1">{page.title}</h4>
              <p className="text-sm text-gray-600">{page.description}</p>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}

// 面包屑导航组件
export function Breadcrumbs({ items }: { items: Array<{ name: string; href?: string }> }) {
  return (
    <nav className="flex mb-6" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {items.map((item, index) => (
          <li key={index} className="inline-flex items-center">
            {index > 0 && (
              <svg
                className="w-6 h-6 text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            )}
            {item.href ? (
              <Link
                href={item.href}
                className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2"
              >
                {item.name}
              </Link>
            ) : (
              <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                {item.name}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

// 关键词密度优化的文本组件
export function SEOText({ 
  children, 
  keywords = [],
  className = ""
}: { 
  children: React.ReactNode;
  keywords?: string[];
  className?: string;
}) {
  return (
    <div className={`seo-optimized-content ${className}`}>
      {children}
      {/* 隐藏的关键词，用于SEO但不影响用户体验 */}
      {keywords.length > 0 && (
        <div className="sr-only">
          {keywords.join(', ')}
        </div>
      )}
    </div>
  );
}

// 页面底部相关链接
export function FooterSEOLinks() {
  const categories = [
    {
      title: "AI Tools",
      links: [
        { text: "Free AI Image Generator", href: "/free-image-generator" },
        { text: "AI Photo Editor", href: "/photo-editor" },
        { text: "AI Background Remover", href: "/background-remover" },
      ]
    },
    {
      title: "Resources",
      links: [
        { text: "How to Use AI Image Generator", href: "/free-image-generator" },
        { text: "AI Art Examples", href: "/" },
        { text: "Image Generation Tips", href: "/free-image-generator" },
      ]
    },
    {
      title: "Company",
      links: [
        { text: "About ImageFox", href: "/about-us" },
        { text: "Contact Support", href: "/contact-us" },
        { text: "Privacy Policy", href: "/privacy-policy" },
        { text: "Terms of Service", href: "/terms-of-use" },
      ]
    }
  ];

  return (
    <div className="grid md:grid-cols-3 gap-8">
      {categories.map((category, index) => (
        <div key={index}>
          <h3 className="font-semibold text-gray-900 mb-4">{category.title}</h3>
          <ul className="space-y-2">
            {category.links.map((link, linkIndex) => (
              <li key={linkIndex}>
                <Link
                  href={link.href}
                  className="text-gray-600 hover:text-blue-600 transition-colors duration-200"
                >
                  {link.text}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
}

// 快速导航组件
export function QuickNavigation() {
  const quickLinks = [
    { text: "Generate AI Images", href: "/free-image-generator", primary: true },
    { text: "Edit Photos", href: "/photo-editor" },
    { text: "Remove Backgrounds", href: "/background-remover" },
    { text: "View Examples", href: "/#case-study" },
  ];

  return (
    <div className="fixed bottom-6 right-6 z-50 hidden lg:block">
      <div className="bg-white shadow-lg rounded-lg p-4 border">
        <h4 className="font-semibold text-sm mb-3">Quick Access</h4>
        <div className="space-y-2">
          {quickLinks.map((link, index) => (
            <Link
              key={index}
              href={link.href}
              className={`block text-sm px-3 py-2 rounded transition-colors duration-200 ${
                link.primary
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              {link.text}
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
