'use client'

import { useState } from 'react';
import Image from 'next/image';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, Calendar, Settings, Palette, Zap, Images } from 'lucide-react';
import { toast } from 'sonner';

const R2_DOMAIN_NAME = 'https://img.imagefox.art'


interface EditImageData {
  id: string;
  imageUrl: string;
  tmpUrl?: string;
  createdAt: string;
  editor: {
    id: string;
    userPrompt?: string;
    prompt: string;
    parameters: any;
    imageUrls: string[];
    creditsUsed: number;
    status: string;
    createdAt: string;
    completedAt?: string;
  };
}

interface EditDetailModalProps {
  editImage: EditImageData | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function EditDetailModal({ 
  editImage, 
  isOpen, 
  onClose
}: EditDetailModalProps) {
  const [isDownloading, setIsDownloading] = useState(false);

  if (!editImage) return null;

  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      const response = await fetch(R2_DOMAIN_NAME + '/' + editImage.imageUrl);
      if (!response.ok) throw new Error('Failed to fetch image');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `edited-image-${editImage.id}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      toast.success('Image downloaded successfully');
    } catch (error) {
      console.error('Error downloading image:', error);
      toast.error('Failed to download image');
    } finally {
      setIsDownloading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="min-w-6xl min-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Edit Details</span>
            <div className="flex gap-2 pr-20">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                disabled={isDownloading}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {isDownloading ? 'Downloading...' : 'Download'}
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Image Display */}
          <div className="space-y-4">
            <div className="relative aspect-square w-full overflow-hidden rounded-lg border">
              <Image
                src={R2_DOMAIN_NAME + '/' + editImage.imageUrl}
                alt="Edited image"
                fill
                className="object-contain"
                priority
              />
            </div>
            
            {/* Image Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Settings className="h-4 w-4" />
                  Image Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Credits Used:</span>
                  <Badge variant="secondary">{editImage.editor.creditsUsed}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <Badge className={getStatusColor(editImage.editor.status)}>
                    {editImage.editor.status}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Created:</span>
                  <span>{formatDate(editImage.createdAt)}</span>
                </div>
                {editImage.editor.completedAt && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Completed:</span>
                    <span>{formatDate(editImage.editor.completedAt)}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Edit Details */}
          <div className="space-y-4">
            {/* Original Images */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Images className="h-4 w-4" />
                  Original Images ({editImage.editor.imageUrls.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  {editImage.editor.imageUrls.slice(0, 4).map((url, index) => (
                    <div key={index} className="relative aspect-square overflow-hidden rounded border">
                      <Image
                        src={ url}
                        alt={`Original image ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ))}
                  {editImage.editor.imageUrls.length > 4 && (
                    <div className="relative aspect-square overflow-hidden rounded border bg-muted flex items-center justify-center">
                      <span className="text-sm text-muted-foreground">
                        +{editImage.editor.imageUrls.length - 4} more
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Prompts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Palette className="h-4 w-4" />
                  Prompts
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {editImage.editor.userPrompt && (
                  <div>
                    <label className="text-xs font-medium text-muted-foreground">User Prompt:</label>
                    <p className="text-sm mt-1 p-2 bg-muted rounded">{editImage.editor.userPrompt}</p>
                  </div>
                )}
                
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Enhanced Prompt:</label>
                  <p className="text-sm mt-1 p-2 bg-muted rounded max-h-32 overflow-y-auto">
                    {editImage.editor.prompt}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Parameters */}
            {/* {editImage.editor.parameters && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-sm">
                    <Settings className="h-4 w-4" />
                    Edit Parameters
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-2 text-xs">
                    {Object.entries(editImage.editor.parameters as Record<string, any>).map(([key, value]) => (
                      <div key={key} className="flex justify-start">
                        <span className="text-muted-foreground capitalize">
                          {key.replace(/_/g, ' ')}:
                        </span>
                        <span className="font-medium">
                          {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )} */}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
