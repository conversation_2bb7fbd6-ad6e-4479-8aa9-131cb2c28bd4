import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import Script from 'next/script'

interface BreadcrumbItem {
  label: string
  href?: string
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
  className?: string
}

export default function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  // 生成面包屑结构化数据
  const breadcrumbStructuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.label,
      ...(item.href && { "item": `https://imagefox.art${item.href}` })
    }))
  }

  return (
    <>
      <nav 
        className={`flex items-center space-x-2 text-sm text-gray-600 mb-4 ${className}`}
        aria-label="Breadcrumb"
      >
        <Link 
          href="/" 
          className="flex items-center hover:text-gray-900 transition-colors"
          aria-label="Home"
        >
          <Home size={16} className="mr-1" />
          <span>Home</span>
        </Link>
        
        {items.map((item, index) => (
          <div key={index} className="flex items-center">
            <ChevronRight size={16} className="mx-2 text-gray-400" />
            {item.href ? (
              <Link 
                href={item.href} 
                className="hover:text-gray-900 transition-colors"
              >
                {item.label}
              </Link>
            ) : (
              <span className="text-gray-900 font-medium">{item.label}</span>
            )}
          </div>
        ))}
      </nav>

      {/* 面包屑结构化数据 */}
      <Script
        id="breadcrumb-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData),
        }}
      />
    </>
  )
}

// 预定义的面包屑配置
export const breadcrumbConfigs = {
  'free-image-generator': [
    { label: 'AI Tools', href: '/#features' },
    { label: 'Image Generator' }
  ],
  'photo-editor': [
    { label: 'AI Tools', href: '/#features' },
    { label: 'Photo Editor' }
  ],
  'photo-editor-multi': [
    { label: 'AI Tools', href: '/#features' },
    { label: 'Photo Editor' },
    { label: 'Multi Editor' }
  ],
  'generate-history': [
    { label: 'Dashboard', href: '/profile' },
    { label: 'Generation History' }
  ],
  'edit-history': [
    { label: 'Dashboard', href: '/profile' },
    { label: 'Edit History' }
  ],
  'about-us': [
    { label: 'About Us' }
  ],
  'privacy-policy': [
    { label: 'Legal', href: '/terms-of-use' },
    { label: 'Privacy Policy' }
  ],
  'terms-of-use': [
    { label: 'Legal' },
    { label: 'Terms of Use' }
  ],
  'contact-us': [
    { label: 'Contact Us' }
  ]
}
