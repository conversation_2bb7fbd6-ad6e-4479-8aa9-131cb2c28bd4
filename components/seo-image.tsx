import Image from 'next/image'
import { useState } from 'react'

interface SEOImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
  sizes?: string
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  loading?: 'lazy' | 'eager'
  title?: string
  caption?: string
  credit?: string
}

export default function SEOImage({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  sizes,
  quality = 85,
  placeholder = 'empty',
  blurDataURL,
  loading = 'lazy',
  title,
  caption,
  credit,
  ...props
}: SEOImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)

  const handleLoad = () => {
    setIsLoaded(true)
  }

  const handleError = () => {
    setHasError(true)
  }

  // 如果图片加载失败，显示占位符
  if (hasError) {
    return (
      <div 
        className={`bg-gray-200 flex items-center justify-center ${className}`}
        style={{ width, height }}
        role="img"
        aria-label={alt}
      >
        <span className="text-gray-500 text-sm">Image not available</span>
      </div>
    )
  }

  return (
    <figure className="relative">
      <div className={`relative overflow-hidden ${className}`}>
        <Image
          src={src}
          alt={alt}
          width={width}
          height={height}
          priority={priority}
          sizes={sizes}
          quality={quality}
          placeholder={placeholder}
          blurDataURL={blurDataURL}
          loading={loading}
          title={title}
          onLoad={handleLoad}
          onError={handleError}
          className={`transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          {...props}
        />
        
        {/* 加载状态 */}
        {!isLoaded && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
          </div>
        )}
      </div>
      
      {/* 图片说明 */}
      {(caption || credit) && (
        <figcaption className="mt-2 text-sm text-gray-600">
          {caption && <div>{caption}</div>}
          {credit && <div className="text-xs text-gray-500 mt-1">{credit}</div>}
        </figcaption>
      )}
    </figure>
  )
}

// 预设的图片尺寸配置
export const imageSizes = {
  hero: {
    width: 1200,
    height: 630,
    sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px'
  },
  card: {
    width: 400,
    height: 300,
    sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 400px'
  },
  thumbnail: {
    width: 150,
    height: 150,
    sizes: '150px'
  },
  gallery: {
    width: 800,
    height: 600,
    sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 70vw, 800px'
  }
}

// 生成优化的blurDataURL
export function generateBlurDataURL(width: number = 10, height: number = 10): string {
  const canvas = document.createElement('canvas')
  canvas.width = width
  canvas.height = height
  const ctx = canvas.getContext('2d')
  
  if (ctx) {
    // 创建简单的渐变作为模糊占位符
    const gradient = ctx.createLinearGradient(0, 0, width, height)
    gradient.addColorStop(0, '#f3f4f6')
    gradient.addColorStop(1, '#e5e7eb')
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, width, height)
  }
  
  return canvas.toDataURL()
}
