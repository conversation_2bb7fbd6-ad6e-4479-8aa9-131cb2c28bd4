'use client'

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar, Zap, ChevronLeft, ChevronRight, ImageIcon, Edit } from 'lucide-react';
import EditDetailModal from './edit-detail-modal';
import { motion } from 'framer-motion';

interface EditImageData {
  id: string;
  imageUrl: string;
  tmpUrl?: string;
  createdAt: string;
  editor: {
    id: string;
    userPrompt?: string;
    prompt: string;
    parameters: any;
    imageUrls: string[];
    creditsUsed: number;
    status: string;
    createdAt: string;
    completedAt?: string;
  };
}

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}

interface EditHistoryGridProps {
  initialEditImages: EditImageData[];
  initialPagination: PaginationData;
}

export default function EditHistoryGrid({ 
  initialEditImages, 
  initialPagination 
}: EditHistoryGridProps) {
  const [editImages, setEditImages] = useState<EditImageData[]>(initialEditImages);
  const [pagination, setPagination] = useState<PaginationData>(initialPagination);
  const [selectedEditImage, setSelectedEditImage] = useState<EditImageData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const fetchEditImages = async (page: number) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/user/edit-images?page=${page}&limit=${pagination.limit}`);
      if (!response.ok) throw new Error('Failed to fetch edit images');
      
      const data = await response.json();
      setEditImages(data.editImages);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching edit images:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageClick = (editImage: EditImageData) => {
    setSelectedEditImage(editImage);
    setIsModalOpen(true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const truncateText = (text: string, maxLength: number) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (editImages.length === 0 && !isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <Edit size={64} className="text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold text-gray-600 mb-2">No edited images yet</h3>
        <p className="text-gray-500 mb-6">Start editing images with our AI editor!</p>
        <Button asChild>
          <a href="/editor-multi">Edit Your First Image</a>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Images Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: pagination.limit }).map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-0">
                <Skeleton className="aspect-square w-full" />
                <div className="p-3 space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          editImages.map((editImage, index) => (
            <motion.div
              key={editImage.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <Card 
                className="overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-300 group"
                onClick={() => handleImageClick(editImage)}
              >
                <CardContent className="p-0">
                  {/* Image */}
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={'https://img.imagefox.art/' + editImage.imageUrl}
                      alt={editImage.editor.userPrompt || editImage.editor.prompt}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                      loading="lazy"
                    />
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                    
                    {/* Status badge */}
                    <div className="absolute top-2 right-2">
                      <Badge className={getStatusColor(editImage.editor.status)}>
                        {editImage.editor.status}
                      </Badge>
                    </div>
                    
                    {/* Original images count */}
                    <div className="absolute bottom-2 left-2">
                      <Badge variant="secondary" className="text-xs">
                        {editImage.editor.imageUrls.length} images
                      </Badge>
                    </div>
                  </div>
                  
                  {/* Content */}
                  <div className="p-3 space-y-2">
                    <p className="text-sm font-medium line-clamp-2">
                      {truncateText(
                        editImage.editor.userPrompt || editImage.editor.prompt, 
                        60
                      )}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDate(editImage.createdAt)}
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Zap className="h-3 w-3" />
                        {editImage.editor.creditsUsed}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-center gap-2 pt-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchEditImages(pagination.currentPage - 1)}
            disabled={!pagination.hasPrevPage || isLoading}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              const pageNum = Math.max(1, pagination.currentPage - 2) + i;
              if (pageNum > pagination.totalPages) return null;
              
              return (
                <Button
                  key={pageNum}
                  variant={pageNum === pagination.currentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => fetchEditImages(pageNum)}
                  disabled={isLoading}
                  className="w-8 h-8 p-0"
                >
                  {pageNum}
                </Button>
              );
            })}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchEditImages(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage || isLoading}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Edit Detail Modal */}
      <EditDetailModal
        editImage={selectedEditImage}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}
