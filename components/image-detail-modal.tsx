'use client'

import { useState } from 'react';
import Image from 'next/image';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, Heart, HeartIcon, Calendar, Settings, Palette, Zap } from 'lucide-react';
import { toast } from 'sonner';

interface ImageData {
  id: string;
  imageUrl: string;
  thumbnailUrl?: string;
  isFavorite: boolean;
  generation: {
    id: string;
    userPrompt: string;
    prompt: string;
    negativePrompt?: string;
    parameters: any;
    createdAt: string;
    creditsUsed: number;
    model: {
      modelName: string;
      provider: string;
      type: string
    };
  };
}

interface ImageDetailModalProps {
  image: ImageData | null;
  isOpen: boolean;
  onClose: () => void;
  onFavoriteToggle?: (imageId: string, isFavorite: boolean) => void;
}

export default function ImageDetailModal({ 
  image, 
  isOpen, 
  onClose, 
  onFavoriteToggle 
}: ImageDetailModalProps) {
  const [isTogglingFavorite, setIsTogglingFavorite] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  if (!image) return null;

  const handleFavoriteToggle = async () => {
    if (!onFavoriteToggle) return;
    
    setIsTogglingFavorite(true);
    try {
      const response = await fetch(`/api/user/images/${image.id}/favorite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to toggle favorite');
      }

      const result = await response.json();
      onFavoriteToggle(image.id, result.isFavorite);
      
      toast.success(
        result.isFavorite ? 'Added to favorites' : 'Removed from favorites'
      );
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast.error('Failed to update favorite status');
    } finally {
      setIsTogglingFavorite(false);
    }
  };

  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      const response = await fetch(image.imageUrl);
      if (!response.ok) throw new Error('Failed to fetch image');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `generated-image-${image.id}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      toast.success('Image downloaded successfully');
    } catch (error) {
      console.error('Error downloading image:', error);
      toast.error('Failed to download image');
    } finally {
      setIsDownloading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getImageDimensions = () => {
    const params = image.generation.parameters;
    if (params && typeof params === 'object') {
      return `${params.width || 'Unknown'} × ${params.height || 'Unknown'}`;
    }
    return 'Unknown';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="min-w-6xl min-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Image Details</span>
            <div className="flex gap-2 pr-20">
              <Button
                variant="outline"
                size="sm"
                onClick={handleFavoriteToggle}
                disabled={isTogglingFavorite}
                className="flex items-center gap-2"
              >
                {image.isFavorite ? (
                  <Heart className="h-4 w-4 fill-red-500 text-red-500" />
                ) : (
                  <HeartIcon className="h-4 w-4" />
                )}
                {isTogglingFavorite ? 'Updating...' : (image.isFavorite ? 'Favorited' : 'Favorite')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                disabled={isDownloading}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {isDownloading ? 'Downloading...' : 'Download'}
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Image Display */}
          <div className="space-y-4">
            <div className="relative aspect-square w-full overflow-hidden rounded-lg border">
              <Image
                src={image.imageUrl}
                alt="Generated image"
                fill
                className="object-contain"
                priority
              />
            </div>
            
            {/* Image Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Settings className="h-4 w-4" />
                  Image Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Dimensions:</span>
                  <span>{getImageDimensions()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Credits Used:</span>
                  <Badge variant="secondary">{image.generation.creditsUsed}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <Badge variant="default">Completed</Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Generation Details */}
          <div className="space-y-4">
            {/* Model Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Zap className="h-4 w-4" />
                  Model Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                  <span className="text-muted-foreground">Type:</span>
                  <span className="font-medium">{image.generation.model.type}</span>
                </div>
                {/* <div className="flex justify-between">
                  <span className="text-muted-foreground">Model:</span>
                  <span className="font-medium">{image.generation.model.modelName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Provider:</span>
                  <span>{image.generation.model.provider}</span>
                </div> */}
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Generated:</span>
                  <span>{formatDate(image.generation.createdAt)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Prompts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Palette className="h-4 w-4" />
                  Prompts
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-xs font-medium text-muted-foreground">User Prompt:</label>
                  <p className="text-sm mt-1 p-2 bg-muted rounded">{image.generation.userPrompt}</p>
                </div>
                
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Enhanced Prompt:</label>
                  <p className="text-sm mt-1 p-2 bg-muted rounded max-h-32 overflow-y-auto">
                    {image.generation.prompt}
                  </p>
                </div>

                {image.generation.negativePrompt && (
                  <div>
                    <label className="text-xs font-medium text-muted-foreground">Negative Prompt:</label>
                    <p className="text-sm mt-1 p-2 bg-muted rounded">
                      {image.generation.negativePrompt}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Parameters */}
            {image.generation.parameters && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-sm">
                    <Settings className="h-4 w-4" />
                    Generation Parameters
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    {Object.entries(image.generation.parameters as Record<string, any>).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="text-muted-foreground capitalize">
                          {key.replace(/_/g, ' ')}:
                        </span>
                        <span className="font-medium">
                          {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
