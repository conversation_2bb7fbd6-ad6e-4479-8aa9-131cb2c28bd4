import { ImageIcon, Loader2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function Uploader({onUpload }: { onUpload: (urls: string[]) => void, }) {

    const [uploading, setUploading] = useState(false);

    function handleUploadImage() {
            //点击上传图片
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;
            input.accept = 'image/*';
            input.click();
            input.onchange = async (e) => {
                const files = (e.target as HTMLInputElement).files;
                setUploading(true);
                if (files) {
                    console.log('files', files);
                    const formData = new FormData();
                    
                    for (let i = 0; i < files.length; i++) {
                        formData.append('file', files[i]);
                    }
                    
                    const response = await fetch('/api/upload', {
                        method: 'POST',
                        body: formData,
                    });
                    const uploadResult = await response.json();
                    setUploading(false);
                    if(uploadResult.status === 'ok'){
                        onUpload([...uploadResult.urls]);
                    } else {
                        toast.error(uploadResult.error);
                    }
                }
            };
        }

    return (
            <div onClick={handleUploadImage} className="cursor-pointer border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-4 flex items-center justify-center h-[100px] w-[100px]">
                <label
                htmlFor="add-more-files"
                className="cursor-pointer flex flex-col items-center"
                >
                <ImageIcon className="h-8 w-8 text-gray-400 dark:text-gray-600" />
                <span className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    {uploading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : "Add more"}
                </span>
                </label>
            </div>

    );
}