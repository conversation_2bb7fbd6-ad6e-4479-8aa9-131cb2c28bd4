'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Turnstile } from '@marsidev/react-turnstile';
import type { TurnstileInstance } from '@marsidev/react-turnstile';
import { toast } from 'sonner';
import { Loader2, Download, Upload, RefreshCw, Image as ImageIcon, Wand2, ShieldCheck, Zap } from 'lucide-react';
import Image from 'next/image';
import ReactCompareImage from 'react-compare-image';

interface BackgroundRemovalResult {
  success: boolean;
  originalImageUrl: string;
  processedImageUrl: string;
}

export default function BackgroundRemover() {
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<BackgroundRemovalResult | null>(null);
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);
  const [siteKey, setSiteKey] = useState<string>('');
  
  const turnstileRef = useRef<TurnstileInstance | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetch('/api/turnstile')
      .then(res => res.json())
      .then(data => setSiteKey(data.siteKey))
      .catch(err => console.error('Failed to get site key:', err));
  }, []);

  const handleFileUpload = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file.');
      return;
    }
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size cannot exceed 5MB.');
      return;
    }
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      const uploadResult = await response.json();
      if (uploadResult.status === 'ok') {
        setUploadedImage(uploadResult.urls[0]);
        setResult(null);
        toast.success('Image uploaded successfully.');
      } else {
        toast.error(uploadResult.error || 'Upload failed.');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Upload failed.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) handleFileUpload(files[0]);
  };

  const handleDragOver = (e: React.DragEvent) => e.preventDefault();

  const handleUploadClick = () => fileInputRef.current?.click();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) handleFileUpload(files[0]);
  };

  const handleRemoveBackground = async () => {
    if (!uploadedImage) return toast.error('Please upload an image first.');
    if (!turnstileToken) return toast.error('Please complete the human verification.');

    setIsProcessing(true);
    toast.info('Processing, please wait...');
    try {
      const response = await fetch('/api/background-remove', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ imageUrl: uploadedImage, turnstileToken }),
      });
      const data = await response.json();
      if (data.success && data.processedImageUrl) {
        setResult(data);
        toast.success('Background removal complete!');
        resetTurnstile();
      } else {
        toast.error(data.error || 'Processing failed.');
      }
    } catch (error) {
      console.error('Background removal error:', error);
      toast.error('Processing failed, please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownload = async (imageUrl: string) => {
    try {
      if (imageUrl.startsWith('blob:') || imageUrl.startsWith('data:')) {
        const a = document.createElement('a');
        a.href = imageUrl;
        a.download = `background-removed-${Date.now()}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        toast.success('Image downloaded successfully.');
        return;
      }
      const response = await fetch(`/api/download?url=${encodeURIComponent(imageUrl)}`);
      if (!response.ok) throw new Error('Download failed');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `background-removed-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Image downloaded successfully.');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Download failed.');
    }
  };

  const resetTurnstile = () => {
    turnstileRef.current?.reset();
    setTurnstileToken(null);
  };

  const handleReset = () => {
    setUploadedImage(null);
    setResult(null);
    resetTurnstile();
  }

  return (
    <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 lg:py-10">
      {/* Hero Section */}
      <section className="text-center space-y-4 mb-12 md:mb-16">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight">
          Free Online AI Background Remover Tool
        </h1>
        <p className="max-w-3xl mx-auto text-lg md:text-xl text-muted-foreground">
          Upload your image, and our intelligent AI will automatically remove the background for you in seconds. 100% free, HD quality, no watermarks, and no registration required.
        </p>
      </section>

      {/* Main Tool Section */}
      <Card className="w-full max-w-2xl mx-auto shadow-lg">
        <CardContent className="p-4 sm:p-6">
          {!result ? (
            <div className='space-y-6'>
              <div
                className="relative border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-8 text-center cursor-pointer hover:border-primary dark:hover:border-primary transition-colors duration-300 ease-in-out bg-gray-50 dark:bg-gray-900"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={handleUploadClick}
              >
                <input ref={fileInputRef} type="file" accept="image/*" onChange={handleFileChange} className="hidden" />
                {isUploading ? (
                  <div className="flex flex-col items-center justify-center space-y-2 h-48">
                    <Loader2 className="h-10 w-10 animate-spin text-primary" />
                    <p className='text-lg'>Uploading...</p>
                  </div>
                ) : uploadedImage ? (
                  <div className='space-y-4'>
                    <Image src={uploadedImage} alt="Uploaded image" width={300} height={200} className="mx-auto rounded-lg object-contain max-h-48" />
                    <p className="text-sm text-muted-foreground">Click to re-upload</p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center space-y-4 h-48">
                    <Upload className="h-12 w-12 text-gray-400" />
                    <p className="text-lg font-medium">Click or drag image here</p>
                    <p className="text-sm text-muted-foreground">Supports JPG, PNG, WEBP, Max 5MB</p>
                  </div>
                )}
              </div>
              {uploadedImage && siteKey && (
                <div className="flex flex-col items-center space-y-4">
                  <div className="flex items-center space-x-2">
                    <Turnstile ref={turnstileRef} siteKey={siteKey} onSuccess={setTurnstileToken} />
                    <Button variant="outline" size="icon" onClick={resetTurnstile}><RefreshCw className="h-4 w-4" /></Button>
                  </div>
                  <Button onClick={handleRemoveBackground} disabled={!turnstileToken || isProcessing} size="lg" className="w-full max-w-xs">
                    {isProcessing ? <><Loader2 className="h-5 w-5 mr-2 animate-spin" />Processing...</> : <><Wand2 className="h-5 w-5 mr-2" />Remove Background</>}
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              <h3 className="text-2xl font-semibold text-center">Processing Complete!</h3>
              <div className="max-w-full mx-auto rounded-lg overflow-hidden shadow-md">
                <ReactCompareImage
                  leftImage={result.originalImageUrl}
                  rightImage={result.processedImageUrl}
                  leftImageLabel="Original"
                  rightImageLabel="Background Removed"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Button onClick={() => handleDownload(result.processedImageUrl)} size="lg">
                  <Download className="h-5 w-5 mr-2" />Download HD Image
                </Button>
                <Button onClick={handleReset} variant="outline" size="lg">
                  <RefreshCw className="h-5 w-5 mr-2" />Process New Image
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      {/* How It Works Section */}
      <section className="py-16 md:py-24">
        <h2 className="text-3xl font-bold text-center mb-12">Easily Remove Backgrounds in Three Steps</h2>
        <div className="grid md:grid-cols-3 gap-8 text-center">
          <div className="space-y-4">
            <div className="w-20 h-20 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
              <Upload className="w-10 h-10 text-primary" />
            </div>
            <h3 className="text-xl font-semibold">1. Upload Image</h3>
            <p className="text-muted-foreground">Click the upload button or drag and drop your image file into the designated area.</p>
          </div>
          <div className="space-y-4">
            <div className="w-20 h-20 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
              <Wand2 className="w-10 h-10 text-primary" />
            </div>
            <h3 className="text-xl font-semibold">2. AI Auto-Processing</h3>
            <p className="text-muted-foreground">Our artificial intelligence will automatically detect and accurately remove the image background.</p>
          </div>
          <div className="space-y-4">
            <div className="w-20 h-20 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
              <Download className="w-10 h-10 text-primary" />
            </div>
            <h3 className="text-xl font-semibold">3. Download Image</h3>
            <p className="text-muted-foreground">Preview the result, and if satisfied, download the HD PNG image with a transparent background.</p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 md:py-24 bg-secondary/50 rounded-lg px-6">
        <h2 className="text-3xl font-bold text-center mb-12">Feature Highlights</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="flex flex-col items-center text-center space-y-3">
            <ImageIcon className="w-12 h-12 text-primary" />
            <h3 className="text-xl font-semibold">HD Quality</h3>
            <p className="text-muted-foreground">Retains original resolution, download lossless HD transparent background images.</p>
          </div>
          <div className="flex flex-col items-center text-center space-y-3">
            <Zap className="w-12 h-12 text-primary" />
            <h3 className="text-xl font-semibold">Lightning Fast Processing</h3>
            <p className="text-muted-foreground">AI-driven, completes complex background removal tasks in just a few seconds.</p>
          </div>
          <div className="flex flex-col items-center text-center space-y-3">
            <Wand2 className="w-12 h-12 text-primary" />
            <h3 className="text-xl font-semibold">Smart & Precise</h3>
            <p className="text-muted-foreground">Accurately identifies the subject, whether it's people, products, or animals, for perfect cutouts.</p>
          </div>
          <div className="flex flex-col items-center text-center space-y-3">
            <ShieldCheck className="w-12 h-12 text-primary" />
            <h3 className="text-xl font-semibold">Privacy & Security</h3>
            <p className="text-muted-foreground">We respect your privacy. Uploaded images are automatically deleted within 24 hours after processing.</p>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 md:py-24 max-w-3xl mx-auto">
        <h2 className="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h2>
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="item-1">
            <AccordionTrigger>Is this tool completely free?</AccordionTrigger>
            <AccordionContent>Yes, our online background removal tool is completely free with no hidden charges. You can use it anytime without registration.</AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger>What image formats do I need?</AccordionTrigger>
            <AccordionContent>We support common image formats including JPG, PNG, and WEBP. For best results, please upload clear images with a well-defined subject.</AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger>Is there a size limit for uploaded images?</AccordionTrigger>
            <AccordionContent>Yes, we currently support image files up to 5MB. If your image exceeds this size, please compress it first.</AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger>Is my image data secure?</AccordionTrigger>
            <AccordionContent>We take your data privacy very seriously. All uploaded images will be permanently deleted from our servers 24 hours after processing. Please use it with confidence.</AccordionContent>
          </AccordionItem>
        </Accordion>
      </section>
    </div>
  );
}
