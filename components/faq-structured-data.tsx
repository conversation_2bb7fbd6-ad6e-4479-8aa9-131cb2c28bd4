import Script from 'next/script'

interface FAQItem {
  question: string
  answer: string
}

interface FAQStructuredDataProps {
  faqs: FAQItem[]
}

export default function FAQStructuredData({ faqs }: FAQStructuredDataProps) {
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }

  return (
    <Script
      id="faq-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(faqStructuredData),
      }}
    />
  )
}

// 预定义的FAQ数据
export const imageFoxFAQs: FAQItem[] = [
  {
    question: "Who owns the copyright to generated images?",
    answer: "Users have full commercial usage rights to the generated images, which can be used for commercial projects, product designs, and marketing materials."
  },
  {
    question: "Do you support batch generation via API?",
    answer: "API calls are currently not supported."
  },
  {
    question: "What is the maximum supported resolution?",
    answer: "Basic plan supports up to 1024px, Pro plan supports up to 2048px, and Enterprise plan supports ultra-HD output up to 4096px."
  },
  {
    question: "Do you support custom model training?",
    answer: "Custom model training is currently not supported."
  },
  {
    question: "What factors affect generation speed?",
    answer: "Image complexity, resolution, and current system load. Average generation time: <15 seconds for 512px, <30 seconds for 1024px."
  },
  {
    question: "Are there any limitations on the free plan?",
    answer: "20 basic resolution images per month, watermark-free and available for commercial use."
  },
  {
    question: "What happens to unused credits when upgrading a subscription?",
    answer: "Free plan credits will be reset to zero. For paid plans, unused credits will be prorated and carried over to the new billing cycle."
  },
  {
    question: "What payment methods do you accept?",
    answer: "Credit cards (Visa/Mastercard/Amex) and PayPal."
  },
  {
    question: "Can I cancel my subscription? What's the refund policy?",
    answer: "You can cancel anytime, and you'll receive a prorated refund for unused months."
  },
  {
    question: "Do you support image editing/expansion?",
    answer: "Image editing/expansion is currently not supported."
  },
  {
    question: "Can it be integrated into design workflows?",
    answer: "Currently not supported."
  }
]
