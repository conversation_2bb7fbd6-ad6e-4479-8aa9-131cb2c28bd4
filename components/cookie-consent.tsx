"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Cookie, Settings, Shield, BarChart3, Target, X, ChevronUp, Info, Check } from "lucide-react"

interface CookieSettings {
  necessary: boolean
  analytics: boolean
  marketing: boolean
  preferences: boolean
}

interface CookieConsentProps {
  onAccept?: (settings: CookieSettings) => void
  onReject?: () => void
  companyName?: string
  privacyPolicyUrl?: string
}

const handleAccept = (settings: any) => {
    console.log("Cookie settings saved:", settings)
    // You can integrate your analytics tools here
  }

  const handleReject = () => {
    console.log("User rejected non-essential cookies")
  }


export default function CookieConsent({
  onAccept,
  onReject,
  companyName = "Our Website",
  privacyPolicyUrl = "/privacy-policy",
}: CookieConsentProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [isAccepted, setIsAccepted] = useState(false)
  const [settings, setSettings] = useState<CookieSettings>({
    necessary: true, // 必要cookie始终启用
    analytics: false,
    marketing: false,
    preferences: false,
  })

  // 检查是否已经设置过cookie偏好
  useEffect(() => {
    const cookieConsent = localStorage.getItem("cookie-consent")
    if (!cookieConsent) {
      // Delay display to improve user experience
      const timer = setTimeout(() => setIsVisible(true), 1000)
      return () => clearTimeout(timer)
    } else {
      setIsAccepted(true)
    }
  }, [])

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
    }
    setSettings(allAccepted)
    saveCookiePreferences(allAccepted)
    setIsVisible(false)
    setIsAccepted(true)
    onAccept?.(allAccepted)
  }

  const handleRejectAll = () => {
    const onlyNecessary = {
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false,
    }
    setSettings(onlyNecessary)
    saveCookiePreferences(onlyNecessary)
    setIsVisible(false)
    setIsAccepted(true)
    onReject?.()
  }

  const handleSavePreferences = () => {
    saveCookiePreferences(settings)
    setIsVisible(false)
    setIsAccepted(true)
    onAccept?.(settings)
  }

  const saveCookiePreferences = (preferences: CookieSettings) => {
    localStorage.setItem(
      "cookie-consent",
      JSON.stringify({
        settings: preferences,
        timestamp: new Date().toISOString(),
      }),
    )
  }

  const handleSettingChange = (key: keyof CookieSettings, value: boolean) => {
    if (key === "necessary") return // Necessary cookies cannot be disabled
    setSettings((prev) => ({ ...prev, [key]: value }))
  }

  const reopenSettings = () => {
    setIsVisible(true)
    setShowDetails(true)
  }

  const handleClose = () => {
    setShowDetails(false)
    setIsVisible(false)
  }

  const cookieTypes = [
    {
      key: "necessary" as keyof CookieSettings,
      title: "Necessary Cookies",
      description: "These cookies are essential for the website to function properly and cannot be disabled. They are usually set only in response to actions made by you.",
      icon: Shield,
      required: true,
      examples: "Login status, shopping cart, security settings",
    },
    {
      key: "analytics" as keyof CookieSettings,
      title: "Analytics Cookies",
      description: "These cookies help us understand how visitors interact with the website by collecting and reporting information anonymously.",
      icon: BarChart3,
      required: false,
      examples: "Google Analytics, page visit statistics",
    },
    {
      key: "marketing" as keyof CookieSettings,
      title: "Marketing Cookies",
      description: "These cookies are used to track visitor activity on the website with the aim of displaying relevant and personalized advertisements.",
      icon: Target,
      required: false,
      examples: "Facebook Pixel, Google Ads, retargeting ads",
    },
    {
      key: "preferences" as keyof CookieSettings,
      title: "Preference Cookies",
      description: "These cookies enable the website to remember your choices and provide you with a more personalized experience.",
      icon: Settings,
      required: false,
      examples: "Language settings, theme preferences, region selection",
    },
  ]
  
  if (!isVisible && !isAccepted) return null

  return (
    <>
      {/* Cookie同意弹窗 */}
      {isVisible && (
        <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-white border-t shadow-lg animate-in slide-in-from-bottom-4 duration-300">
          {/* Background overlay */}

          {/* Popup content */}
          <Card className="w-full max-w-none overflow-visible">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Cookie className="w-6 h-6 text-blue-600" />
                  </div>
                  <div >
                    <CardTitle className="text-xl">Cookie Settings</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">Manage your privacy preferences</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" onClick={handleClose} className="h-8 w-8 p-0">
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="space-y-4 overflow-visible">
              {/* Main description */}
              <div className="space-y-3">
                <p className="text-gray-700 leading-relaxed">
                  {companyName} uses cookies to improve your browsing experience, analyze website traffic, and personalize content.
                  You can choose to accept all cookies or customize your preferences.
                </p>

                <div className="flex items-center gap-2 text-sm text-blue-600">
                  <Info className="w-4 h-4" />
                  <a href={privacyPolicyUrl} className="hover:underline">
                    View our Privacy Policy
                  </a>
                </div>
              </div>

              {/* Quick action buttons */}
              {!showDetails && (
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button onClick={handleAcceptAll} className="flex-1 bg-blue-600 hover:bg-blue-700">
                    <Check className="w-4 h-4 mr-2" />
                    Accept All
                  </Button>
                  <Button variant="outline" onClick={handleRejectAll} className="flex-1">
                    Necessary Cookies Only
                  </Button>
                  <Button variant="ghost" onClick={() => setShowDetails(true)} className="flex-1">
                    <Settings className="w-4 h-4 mr-2" />
                    Customize Settings
                  </Button>
                </div>
              )}

              {/* Detailed settings */}
              {showDetails && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Cookie Types</h3>
                    <Button variant="ghost" size="sm" onClick={() => setShowDetails(false)}>
                      <ChevronUp className="w-4 h-4 mr-1" />
                      收起
                    </Button>
                  </div>

                  <div className="space-y-2">
                    {cookieTypes.map((type, index) => (
                      <div key={type.key}>
                        <div className="flex items-start justify-between p-2 border rounded-lg">
                          <div className="flex items-start gap-2 flex-1">
                            <div className="p-1 bg-gray-100 rounded-lg mt-1">
                              <type.icon className="w-4 h-4 text-gray-600" />
                            </div>
                            <div className="flex-1 space-y-1">
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium text-sm">{type.title}</h4>
                                {type.required && (
                                  <Badge variant="secondary" className="text-xs" >
                                    Required
                                  </Badge>
                                )}
                              </div>
                              <p className="text-xs text-gray-600 leading-relaxed">{type.description}</p>
                              <p className="text-xs text-gray-500">
                                <span className="font-medium">Examples: </span>
                                {type.examples}
                              </p>
                            </div>
                          </div>
                          <Switch
                            checked={settings[type.key]}
                            onCheckedChange={(checked) => handleSettingChange(type.key, checked)}
                            disabled={type.required}
                            className="ml-2"
                          />
                        </div>
                        {index < cookieTypes.length - 1 && <Separator />}
                      </div>
                    ))}
                  </div>

                  {/* Action buttons for detailed settings */}
                  <div className="flex flex-col sm:flex-row gap-2 pt-2 border-t">
                    <Button onClick={handleSavePreferences} className="flex-1 bg-blue-600 hover:bg-blue-700 text-sm">
                      Save Preferences
                    </Button>
                    <Button variant="outline" onClick={handleAcceptAll} className="flex-1 text-sm">
                      Accept All
                    </Button>
                    <Button variant="ghost" onClick={handleRejectAll} className="flex-1 text-sm">
                      Necessary Cookies Only
                    </Button>
                  </div>
                </div>
              )}

              {/* Bottom information */}
              <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                <p>You can change these preferences at any time in the website settings. Some features may require specific cookies to function correctly.</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Floating button to reopen settings */}
      {isAccepted && !isVisible && (
        <Button onClick={reopenSettings} className="fixed bottom-4 left-4 z-40 shadow-lg" size="sm" variant="outline">
          <Cookie className="w-4 h-4 mr-2" />
          Cookie Settings
        </Button>
      )}
    </>
  )
}
