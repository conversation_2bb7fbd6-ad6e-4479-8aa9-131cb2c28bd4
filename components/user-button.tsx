import { signOut, useSession } from "next-auth/react"
import { Image, LogOut, User, WandSparkles } from "lucide-react"
import { But<PERSON> } from "./ui/button"
import Link from "next/link"
import { <PERSON>ubar, MenubarContent, MenubarMenu, MenubarTrigger, MenubarItem, MenubarSeparator } from "./ui/menubar"

export default function UserButton() {
  const session = useSession()

  return (
    <div className="flex items-center space-x-4">
      {session.data?.user ? (
        <Menubar className="border-none outline-none shadow-none">
          <MenubarMenu>
            <MenubarTrigger asChild>
                <Button variant="outline" className="cursor-pointer border-none outline-none shadow-none" size="icon">                
                <User size={20} />
                </Button>
            </MenubarTrigger>
            <MenubarContent align="end" className="w-48">
              <MenubarItem asChild>
                <Link href="/profile" className="cursor-pointer flex items-center w-full">
                  <User size={16} className="mr-2" />
                  Profile
                </Link>
              </MenubarItem>
              <MenubarItem asChild>
                <Link href="/generate-history" className="cursor-pointer flex items-center w-full">
                  <Image size={16} className="mr-2" />
                  Generate History
                </Link>
              </MenubarItem>
              <MenubarItem asChild>
                <Link href="/edit-history" className="cursor-pointer flex items-center w-full">
                  <WandSparkles size={16} className="mr-2" />
                  Edit History
                </Link>
              </MenubarItem>
              <MenubarSeparator />
              <MenubarItem
                className="cursor-pointer text-red-600 focus:text-red-600"
                onClick={() => signOut({redirect:true, callbackUrl: "/" })}
              >
                <LogOut size={16} className="mr-2" />
                Sign Out
              </MenubarItem>
            </MenubarContent>
          </MenubarMenu>
        </Menubar>
      ) : (
        <>
        <Link href="/sign-in"><button className="text-black hover:text-[#333333] transition-colors cursor-pointer">Sign In</button></Link>
        <Link href="/sign-up"><Button className="cursor-pointer bg-[#121212] text-white hover:bg-[#333333] hover:scale-105 transition-all duration-300">
          Sign Up
        </Button></Link>
        </>
      )}
    </div>
    )
}