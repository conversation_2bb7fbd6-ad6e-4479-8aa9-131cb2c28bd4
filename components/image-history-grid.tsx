'use client'

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Heart, Calendar, Zap, ChevronLeft, ChevronRight, ImageIcon } from 'lucide-react';
import ImageDetailModal from './image-detail-modal';
import { motion } from 'framer-motion';

interface ImageData {
  id: string;
  imageUrl: string;
  thumbnailUrl?: string;
  isFavorite: boolean;
  generation: {
    id: string;
    userPrompt: string;
    prompt: string;
    negativePrompt?: string;
    parameters: any;
    createdAt: string;
    creditsUsed: number;
    model: {
      modelName: string;
      provider: string;
      type: string;
    };
  };
}

interface PaginationData {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}

interface ImageHistoryGridProps {
  initialImages: ImageData[];
  initialPagination: PaginationData;
}

export default function ImageHistoryGrid({ 
  initialImages, 
  initialPagination 
}: ImageHistoryGridProps) {
  const [images, setImages] = useState<ImageData[]>(initialImages);
  const [pagination, setPagination] = useState<PaginationData>(initialPagination);
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const fetchImages = async (page: number) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/user/images?page=${page}&limit=${pagination.limit}`);
      if (!response.ok) throw new Error('Failed to fetch images');
      
      const data = await response.json();
      setImages(data.images);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching images:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageClick = (image: ImageData) => {
    setSelectedImage(image);
    setIsModalOpen(true);
  };

  const handleFavoriteToggle = (imageId: string, isFavorite: boolean) => {
    setImages(prev => 
      prev.map(img => 
        img.id === imageId ? { ...img, isFavorite } : img
      )
    );
    
    if (selectedImage && selectedImage.id === imageId) {
      setSelectedImage(prev => prev ? { ...prev, isFavorite } : null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const truncateText = (text: string, maxLength: number) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  if (images.length === 0 && !isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <ImageIcon size={64} className="text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold text-gray-600 mb-2">No images generated yet</h3>
        <p className="text-gray-500 mb-6">Start creating amazing images with our AI generator!</p>
        <Button asChild>
          <a href="/free-image-generator">Generate Your First Image</a>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Images Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: pagination.limit }).map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-0">
                <Skeleton className="aspect-square w-full" />
                <div className="p-3 space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          images.map((image, index) => (
            <motion.div
              key={image.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <Card 
                className="overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-300 group"
                onClick={() => handleImageClick(image)}
              >
                <CardContent className="p-0">
                  {/* Image */}
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={image.thumbnailUrl || image.imageUrl}
                      alt={image.generation.userPrompt}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                      loading="lazy"
                    />
                    
                    {/* Overlay with favorite indicator */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                    
                    {image.isFavorite && (
                      <div className="absolute top-2 right-2">
                        <Heart className="h-4 w-4 fill-red-500 text-red-500" />
                      </div>
                    )}
                    
                    {/* Model badge */}
                    <div className="absolute bottom-2 left-2">
                      {/* <Badge variant="secondary" className="text-xs">
                        {image.generation.model.modelName}
                      </Badge> */}
                    </div>
                  </div>
                  
                  {/* Content */}
                  <div className="p-3 space-y-2">
                    <p className="text-sm font-medium line-clamp-2">
                      {truncateText(image.generation.userPrompt, 60)}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDate(image.generation.createdAt)}
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Zap className="h-3 w-3" />
                        {image.generation.creditsUsed}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-center gap-2 pt-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchImages(pagination.currentPage - 1)}
            disabled={!pagination.hasPrevPage || isLoading}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              const pageNum = Math.max(1, pagination.currentPage - 2) + i;
              if (pageNum > pagination.totalPages) return null;
              
              return (
                <Button
                  key={pageNum}
                  variant={pageNum === pagination.currentPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => fetchImages(pageNum)}
                  disabled={isLoading}
                  className="w-8 h-8 p-0"
                >
                  {pageNum}
                </Button>
              );
            })}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchImages(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage || isLoading}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}

      {/* Image Detail Modal */}
      <ImageDetailModal
        image={selectedImage}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onFavoriteToggle={handleFavoriteToggle}
      />
    </div>
  );
}
