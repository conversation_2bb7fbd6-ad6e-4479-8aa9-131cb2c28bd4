'use server'

import { enhanceUserPrompt } from "@/lib/ai/image";
import { prisma } from "@/lib/prisma";
import { SubscriptionType } from "@/lib/generated/prisma";
import { editorInputSchema, EditorInput } from "@/lib/schemas/editor.schema";

interface APIResponse {
  taskId: string;
  status: "OK" | "FAILED";
  metadata: any;
}

const MODEL_CREDITS: number = 10;


async function falFluxKontext({input, webhook}:{input: any, webhook: string}): Promise<APIResponse> {
      // Add generation ID to the request headers to track it
      const api = `https://queue.fal.run/fal-ai/flux-pro/kontext?fal_webhook=${webhook}`;
       const responseApi = await fetch(api, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Key ${process.env.FAL_KEY}`
          },
          body: JSON.stringify(input)
        });
      const responseData = await responseApi.json();
      if (responseData?.request_id) {
        return {taskId: responseData.request_id, status: "OK", metadata: responseData};
      }
      return {taskId: "", status: "FAILED", metadata: responseData};
}