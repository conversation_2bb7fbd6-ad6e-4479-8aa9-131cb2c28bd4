import NextAuth from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { prisma } from "./lib/prisma";
import { compare } from "bcryptjs";
import Google from "next-auth/providers/google";
import { verifyTurnstile } from "./lib/cf/turnstile";
import { redirect } from "next/navigation";
import { sendEmail } from "./lib/email/ses";
import { createVerifyEmail } from "./lib/email/verify";

async function createDefaultSubscription(userId: string) {
    await prisma.subscription.create({
        data: {
            userId: userId,
            type: "FREE",
            status: "ACTIVE",
            creditsGrantedPerMonth: 30,
            creditsRemaining: 30,
        }
    });
}

export const { handlers, auth, signIn, signOut} = NextAuth({
    providers: [
        CredentialsProvider({
            name: "Credentials",
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
                token: { label: "Token", type: "hidden" },
            },
            async authorize(credentials) {

                if (!credentials?.email || !credentials?.password || !credentials?.token) {
                    return null
                }
                //check turnstile token
                
                try{
                    const isTurnstileValid = await verifyTurnstile(credentials?.token as string)
                    if (!isTurnstileValid) {
                        return null
                    }
                    const user = await prisma.user.findUnique({
                        where: {
                            email: credentials?.email as string,
                        },
                    });
                    if (!user) {
                        return null
                    }
                    const isPasswordValid = await compare(credentials?.password as string, user.passwordHash as string);
                    if (!isPasswordValid) {
                        return null
                    }
                    return user;
                }catch(error){
                    return null
                }
            },
        }),
        Google({
            clientId: process.env.AUTH_GOOGLE_ID,
            clientSecret: process.env.AUTH_GOOGLE_SECRET,
        })
    ],
    callbacks:{
        signIn: async ({user, account, profile, credentials, email}) => {
            try {
                //check user exist
                let userinfo;
                userinfo = await prisma.user.findUnique({
                    where: {
                        email: user.email as string,
                    },
                    include: {
                        subscription: true,
                    }
                });
                //邮箱注册用户首次登陆，创建默认订阅
                if(userinfo && !userinfo.subscription){
                    await createDefaultSubscription(userinfo.id)
                }
                //google注册用户首次登陆， 创建用户和 默认订阅
                if(!userinfo){
                    //create user
                    userinfo = await prisma.user.create({
                        data: {
                            email: user.email as string,
                            name: profile?.name as string,
                            image: profile?.image as string,
                            emailVerified: null
                        }
                    });
                    await prisma.account.create({
                        data: {
                            userId: userinfo.id,
                            type: account?.type as string,
                            provider: account?.provider as string,
                            providerAccountId: account?.providerAccountId as string,
                            access_token: account?.access_token as string,
                            refresh_token: account?.refresh_token as string,
                            expires_at: account?.expires_at as number,
                            token_type: account?.token_type as string,
                            scope: account?.scope as string,
                            id_token: account?.id_token as string,
                            session_state: account?.session_state as string,
                        }
                    });
                    await createDefaultSubscription(userinfo.id)
                }
               
                if (!userinfo?.emailVerified) {
                    console.log("user not verified")

                    //const verifyEmail = await createVerifyEmail(user.email as string);
                    //await sendEmail(user.email as string, 'Verify your email', verifyEmail);

                    //return '/pending-verification';
                }
                return true;
            } catch (error) {
                console.log("error in signIn");
                return false; // Explicitly return false on error to prevent login
            }
        },
        //end
        jwt: async ({ token, user }) => {
            if (user) {
                //find user by email    
                const userinfo = await prisma.user.findUnique({
                    where: {
                        email: user.email as string,
                    },
                });
                token.id = userinfo?.id as string; 
                token.email = user.email as string;
                token.name = user.name as string;
                token.image = user.image as string;
            }
            return token;
        },
        session: async ({ session, token }) => { 
            if (session.user && token.id) { 
                session.user.id = token.id as string; 
                session.user.email = token.email as string;
                session.user.name = token.name as string;
                session.user.image = token.image as string;
                
                // 获取用户积分和模型信息
                const userWithData = await prisma.user.findUnique({
                    where: {
                        id: token.id as string,
                    },
                    include: {
                        subscription: true,
                    }
                });
                
                if (userWithData && userWithData.subscription) {
                    session.user.credits = userWithData.subscription.creditsRemaining;
                }
                
            }
            return session;
        },
    }
})