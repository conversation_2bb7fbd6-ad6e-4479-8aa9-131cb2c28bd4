'use server'

export async function verifyTurnstile(token:string){
    try{
    const url='https://challenges.cloudflare.com/turnstile/v0/siteverify'
    const response = await fetch(url,{
        method:'POST',
        headers:{
            'Content-Type':'application/json'
        },
        body: JSON.stringify({
            secret:process.env.TURNSTILE_SECRET_KEY,
            response:token
        })
    })
    const data = await response.json()
    if (data.success) {
        return true
    }
    return false
    }catch(error){
        console.log(error)
        return false
    }
}