'use server'

import { SESClient, SendEmailCommand, SendEmailCommandInput } from '@aws-sdk/client-ses';

const client = new SESClient({
    region: process.env.AWS_REGION as string,
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID as string,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY as string
    }
});

// use aws ses to send email
export const sendEmail = async (email: string, subject: string, message: string) => {
    
    if (!process.env.AWS_SES_SOURCE_EMAIL) {
        throw new Error('AWS_SES_SOURCE_EMAIL environment variable is not set');
    }
    
    const params: SendEmailCommandInput = {
      Source: process.env.AWS_SES_SOURCE_EMAIL,
      Destination: {
        ToAddresses: [email],
        CcAddresses: [],
        BccAddresses: []
      },
      Message: {
        Body: {
          Text: {
            Data: message
          }
        },
        Subject: {
          Data: subject
        }
      }
    };
    try {
      const data = await client.send(new SendEmailCommand(params));
      return { success: true, messageId: data.MessageId };
    } catch (err) {
      if (err instanceof Error) {
        console.error('Error sending email:', err.message);
        console.error('Stack trace:', err.stack);
        return { success: false, error: err.message };
      }
      console.error('An unknown error occurred while sending email');
      return { success: false, error: 'An unknown error occurred' };
    }

}