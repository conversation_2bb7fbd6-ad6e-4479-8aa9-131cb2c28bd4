'use server'

import crypto from 'crypto'
const IV_LENGTH = 16;
const ALGORITHM = 'aes-256-cbc';


// Helper function to get the derived key
function getDerivedKey(): Buffer {
    const secret = process.env.AUTH_SECRET;
    if (!secret) {
        // It's crucial to have the AUTH_SECRET set.
        throw new Error('AUTH_SECRET environment variable is not set.');
    }
    // Derive a 32-byte key from the secret using SHA-256.
    // This ensures a consistent key length for AES-256.
    return crypto.createHash('sha256').update(secret).digest();
}

export const generateHash = async (email: string): Promise<string> => {
    const derivedKey = getDerivedKey();
    // 每次加密都生成一个全新的、随机的 IV
    const iv = crypto.randomBytes(IV_LENGTH);

    const cipher = crypto.createCipheriv(ALGORITHM, derivedKey, iv);

    let encrypted = cipher.update(email, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // 将 IV（hex格式）和加密数据（hex格式）用冒号连接
    // IV 是公开的，所以这样做是安全的
    return iv.toString('hex') + ':' + encrypted;
}

export const verifyHash = async (encryptedToken: string): Promise<string | null> => {
    const derivedKey = getDerivedKey();
    try {
        const parts = encryptedToken.split(':');
        if (parts.length !== 2) {
            console.error('Invalid token format: Missing IV separator.');
            return null;
        }

        const ivHex = parts[0];
        const encryptedDataHex = parts[1];

        if (!/^[0-9a-fA-F]+$/.test(ivHex) || ivHex.length !== IV_LENGTH * 2) {
            console.error('Invalid IV format or length in token.');
            return null;
        }
        if (!/^[0-9a-fA-F]+$/.test(encryptedDataHex)) {
            console.error('Invalid encrypted data format in token.');
            return null;
        }

        const iv = Buffer.from(ivHex, 'hex');
        const decipher = crypto.createDecipheriv(ALGORITHM, derivedKey, iv);
        decipher.setAutoPadding(true); // Default is true, but explicit for clarity
        let decrypted = decipher.update(encryptedDataHex, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    } catch (error) {
        console.error('Error verifying hash:', error instanceof Error ? error.message : error);
        // Errors can occur from Buffer.from if hex is invalid, or from decipher operations
        // if the token is malformed, tampered, or decrypted with the wrong key/IV.
        return null;
    }
}


export const createVerifyEmail = async (email: string) => {
    const hash = await generateHash(email);
    return `click the link to verify your email: ${process.env.NEXTAUTH_URL}/verify?k=${hash}`;
}